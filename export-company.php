<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if export is requested
if (!isset($_GET['export']) || $_GET['export'] !== 'csv') {
    header("Location: backup-export.php");
    exit;
}

// Get company information
$company_info = getCompanyInfo();

// Set headers for CSV download
$filename = 'company_settings_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add CSV headers
$headers = [
    'Setting',
    'Value'
];

fputcsv($output, $headers);

// Add company data
$company_data = [
    ['Company Name', $company_info['company_name']],
    ['Email', $company_info['email']],
    ['Phone', $company_info['phone']],
    ['Address', $company_info['address']],
    ['Website', $company_info['website']],
    ['Tax ID', $company_info['tax_id']],
    ['Bank Name', $company_info['bank_name']],
    ['Account Number', $company_info['account_number']],
    ['Routing Number', $company_info['routing_number']],
    ['Invoice Template', $company_info['invoice_template'] ?? '1'],
    ['Logo Path', $company_info['logo_path']],
    ['Created At', $company_info['created_at']],
    ['Updated At', $company_info['updated_at']]
];

foreach ($company_data as $row) {
    fputcsv($output, $row);
}

// Close the file pointer
fclose($output);
exit;
?>
