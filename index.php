<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get dashboard statistics
$total_invoices = getTotalInvoices();
$total_revenue = getTotalRevenue();
$recent_invoices = getRecentInvoices(5);
$pending_invoices = getPendingInvoicesCount();
$overdue_invoices = getOverdueInvoicesCount();
$monthly_revenue = getMonthlyRevenue();
$invoice_status_stats = getInvoiceStatusStats();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Som Milk - Invoice Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
            <p class="text-gray-600">Welcome to Som Milk Invoice Management System</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Invoices -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-som-blue text-white">
                        <i class="fas fa-file-invoice text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Invoices</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_invoices; ?></p>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500 text-white">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($total_revenue, 2); ?></p>
                    </div>
                </div>
            </div>

            <!-- Pending Invoices -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-500 text-white">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $pending_invoices; ?></p>
                    </div>
                </div>
            </div>

            <!-- Overdue Invoices -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-500 text-white">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Overdue</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $overdue_invoices; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Monthly Revenue Chart -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Monthly Revenue</h2>
                <div style="position: relative; height: 300px; width: 100%;">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>

            <!-- Invoice Status Chart -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Invoice Status</h2>
                <div style="position: relative; height: 300px; width: 100%;">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div class="lg:col-span-2">
                <!-- Recent Invoices -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-semibold text-gray-900">Recent Invoices</h2>
                            <a href="invoices.php" class="text-som-blue hover:text-som-light-blue font-medium">View All</a>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_invoices)): ?>
                            <p class="text-gray-500 text-center py-8">No invoices found. <a href="create-invoice.php" class="text-som-blue hover:underline">Create your first invoice</a></p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></p>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($invoice['customer_name']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo date('M d, Y', strtotime($invoice['invoice_date'])); ?></p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold text-gray-900">$<?php echo number_format($invoice['total_amount'], 2); ?></p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <?php echo ucfirst($invoice['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div>
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
                    <div class="space-y-3">
                        <a href="create-invoice.php" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Create New Invoice
                        </a>
                        <a href="invoices.php" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                            <i class="fas fa-list mr-2"></i>
                            View All Invoices
                        </a>
                        <a href="customers.php" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                            <i class="fas fa-users mr-2"></i>
                            Manage Customers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
        // Monthly Revenue Chart
        try {
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            const revenueData = <?php echo json_encode($monthly_revenue); ?>;

            // Validate data
            if (!revenueData || !Array.isArray(revenueData)) {
                console.error('Invalid revenue data:', revenueData);
                throw new Error('Invalid revenue data');
            }

            const revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: revenueData.map(item => item.month_name || 'N/A'),
                    datasets: [{
                        label: 'Revenue ($)',
                        data: revenueData.map(item => parseFloat(item.revenue || 0)),
                        borderColor: '#2563EB',
                        backgroundColor: 'rgba(37, 99, 235, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#2563EB',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#2563EB',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: $' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverRadius: 8
                        }
                    }
                }
            });

            console.log('Revenue chart initialized successfully');

        } catch (error) {
            console.error('Error initializing revenue chart:', error);
            // Display error message in chart container
            const chartContainer = document.getElementById('revenueChart').parentElement;
            chartContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>📊 Chart temporarily unavailable</p><p style="font-size: 14px;">Please refresh the page</p></div>';
        }

        // Invoice Status Chart
        try {
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            const statusData = <?php echo json_encode($invoice_status_stats); ?>;

            // Validate data
            if (!statusData || !Array.isArray(statusData) || statusData.length === 0) {
                console.error('Invalid status data:', statusData);
                throw new Error('Invalid status data');
            }

            const statusColors = {
                'draft': '#6B7280',
                'pending': '#F59E0B',
                'paid': '#059669',
                'overdue': '#DC2626',
                'cancelled': '#9CA3AF',
                'no_data': '#E5E7EB'
            };

            // Format labels properly
            const formatLabel = (status) => {
                if (status === 'no_data') return 'No Data';
                return status.charAt(0).toUpperCase() + status.slice(1);
            };

            const statusChart = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: statusData.map(item => formatLabel(item.status)),
                    datasets: [{
                        data: statusData.map(item => parseInt(item.count) || 0),
                        backgroundColor: statusData.map(item => statusColors[item.status] || '#6B7280'),
                        borderWidth: 3,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 4,
                        hoverBorderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                pointStyle: 'circle',
                                font: {
                                    size: 12,
                                    weight: '500'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#2563EB',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: true
                    }
                }
            });

            console.log('Status chart initialized successfully');

        } catch (error) {
            console.error('Error initializing status chart:', error);
            // Display error message in chart container
            const chartContainer = document.getElementById('statusChart').parentElement;
            chartContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;"><p>📊 Chart temporarily unavailable</p><p style="font-size: 14px;">Please refresh the page</p></div>';
        }
    </script>
</body>
</html>
