<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get dashboard statistics
$total_invoices = getTotalInvoices();
$total_revenue = getTotalRevenue();
$recent_invoices = getRecentInvoices(5);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Som Milk - Invoice Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
            <p class="text-gray-600">Welcome to Som Milk Invoice Management System</p>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Invoices -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-som-blue text-white">
                        <i class="fas fa-file-invoice text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Invoices</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_invoices; ?></p>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500 text-white">
                        <i class="fas fa-dollar-sign text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($total_revenue, 2); ?></p>
                    </div>
                </div>
            </div>

            <!-- This Month -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-500 text-white">
                        <i class="fas fa-calendar-month text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">This Month</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo getMonthlyInvoices(); ?></p>
                    </div>
                </div>
            </div>

            <!-- Pending -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-500 text-white">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo getPendingInvoices(); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div class="lg:col-span-2">
                <!-- Recent Invoices -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-semibold text-gray-900">Recent Invoices</h2>
                            <a href="invoices.php" class="text-som-blue hover:text-som-light-blue font-medium">View All</a>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_invoices)): ?>
                            <p class="text-gray-500 text-center py-8">No invoices found. <a href="create-invoice.php" class="text-som-blue hover:underline">Create your first invoice</a></p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                        <div>
                                            <p class="font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></p>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($invoice['customer_name']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo date('M d, Y', strtotime($invoice['invoice_date'])); ?></p>
                                        </div>
                                        <div class="text-right">
                                            <p class="font-semibold text-gray-900">$<?php echo number_format($invoice['total_amount'], 2); ?></p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <?php echo ucfirst($invoice['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div>
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
                    <div class="space-y-3">
                        <a href="create-invoice.php" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            Create New Invoice
                        </a>
                        <a href="invoices.php" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                            <i class="fas fa-list mr-2"></i>
                            View All Invoices
                        </a>
                        <a href="customers.php" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg flex items-center justify-center transition duration-200">
                            <i class="fas fa-users mr-2"></i>
                            Manage Customers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
</body>
</html>
