<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = "Payment";

// Check if customer is logged in to portal
if (!isset($_SESSION['customer_portal'])) {
    header("Location: customer-portal.php");
    exit;
}

// Get invoice ID from URL
$invoice_id = isset($_GET['invoice']) ? (int)$_GET['invoice'] : 0;

if (!$invoice_id) {
    header("Location: customer-portal.php");
    exit;
}

// Get invoice details
$stmt = $conn->prepare("
    SELECT i.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone, c.address as customer_address
    FROM invoices i 
    LEFT JOIN customers c ON i.customer_id = c.id 
    WHERE i.id = ? AND (i.customer_email = ? OR c.email = ?)
");
$customer_email = $_SESSION['customer_portal']['email'];
$stmt->bind_param("iss", $invoice_id, $customer_email, $customer_email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows == 0) {
    header("Location: customer-portal.php");
    exit;
}

$invoice = $result->fetch_assoc();

// Check if invoice can be paid
if ($invoice['status'] == 'paid') {
    $_SESSION['error_message'] = "This invoice has already been paid.";
    header("Location: customer-portal.php");
    exit;
}

// Handle payment submission (mock payment for demo)
if ($_POST && isset($_POST['process_payment'])) {
    $payment_method = sanitizeInput($_POST['payment_method']);
    $cardholder_name = sanitizeInput($_POST['cardholder_name']);
    $card_number = sanitizeInput($_POST['card_number']);
    $expiry_month = sanitizeInput($_POST['expiry_month']);
    $expiry_year = sanitizeInput($_POST['expiry_year']);
    $cvv = sanitizeInput($_POST['cvv']);
    
    // Basic validation
    $errors = [];
    if (empty($cardholder_name)) $errors[] = "Cardholder name is required.";
    if (empty($card_number) || strlen($card_number) < 16) $errors[] = "Valid card number is required.";
    if (empty($expiry_month) || empty($expiry_year)) $errors[] = "Card expiry date is required.";
    if (empty($cvv) || strlen($cvv) < 3) $errors[] = "Valid CVV is required.";
    
    if (empty($errors)) {
        // Mock payment processing - in real implementation, integrate with payment gateway
        $payment_successful = true; // Simulate successful payment
        
        if ($payment_successful) {
            // Update invoice status to paid
            $stmt = $conn->prepare("UPDATE invoices SET status = 'paid', updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("i", $invoice_id);
            
            if ($stmt->execute()) {
                // Log status change
                $stmt = $conn->prepare("
                    INSERT INTO invoice_status_log (invoice_id, old_status, new_status, notes) 
                    VALUES (?, ?, 'paid', 'Payment processed through customer portal')
                ");
                $old_status = $invoice['status'];
                $stmt->bind_param("iss", $invoice_id, $old_status);
                $stmt->execute();
                
                $_SESSION['success_message'] = "Payment processed successfully! Thank you for your payment.";
                header("Location: customer-portal.php");
                exit;
            } else {
                $errors[] = "Payment processing failed. Please try again.";
            }
        } else {
            $errors[] = "Payment was declined. Please check your card details and try again.";
        }
    }
}

$company_info = getCompanyInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo htmlspecialchars($company_info['company_name']); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#2563EB',
                        'som-light-blue': '#3B82F6',
                        'som-green': '#059669',
                        'som-light-green': '#10B981',
                        'som-gray': '#F8FAFC',
                        'som-dark': '#1E293B',
                        'som-red': '#DC2626',
                        'som-border': '#E2E8F0'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-som-gray min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-som-border">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <?php if (!empty($company_info['logo_path']) && file_exists($company_info['logo_path'])): ?>
                        <img src="<?php echo htmlspecialchars($company_info['logo_path']); ?>" 
                             alt="Company Logo" class="h-10 w-auto mr-3">
                    <?php endif; ?>
                    <div>
                        <h1 class="text-xl font-bold text-som-dark"><?php echo htmlspecialchars($company_info['company_name']); ?></h1>
                        <p class="text-sm text-gray-600">Secure Payment</p>
                    </div>
                </div>
                
                <a href="customer-portal.php" class="text-som-blue hover:text-som-light-blue text-sm font-medium">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Portal
                </a>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Invoice Summary -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-som-dark mb-6">Invoice Summary</h2>
                    
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Invoice Number:</span>
                            <span class="font-medium"><?php echo htmlspecialchars($invoice['invoice_number']); ?></span>
                        </div>
                        
                        <div class="flex justify-between">
                            <span class="text-gray-600">Invoice Date:</span>
                            <span class="font-medium"><?php echo date('M j, Y', strtotime($invoice['invoice_date'])); ?></span>
                        </div>
                        
                        <?php if ($invoice['due_date']): ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Due Date:</span>
                                <span class="font-medium <?php echo strtotime($invoice['due_date']) < time() ? 'text-red-600' : ''; ?>">
                                    <?php echo date('M j, Y', strtotime($invoice['due_date'])); ?>
                                </span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="border-t pt-4">
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total Amount:</span>
                                <span class="text-som-blue">$<?php echo number_format($invoice['total_amount'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt text-green-600 mr-2"></i>
                            <span class="text-sm text-green-800 font-medium">Secure Payment</span>
                        </div>
                        <p class="text-sm text-green-700 mt-1">
                            Your payment information is encrypted and secure. We never store your card details.
                        </p>
                    </div>
                </div>

                <!-- Payment Form -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-bold text-som-dark mb-6">Payment Information</h2>
                    
                    <?php if (!empty($errors)): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <strong>Please fix the following errors:</strong>
                            </div>
                            <ul class="list-disc list-inside">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="process_payment" value="1">
                        
                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
                            <div class="grid grid-cols-2 gap-3">
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="credit_card" checked class="mr-3">
                                    <i class="fas fa-credit-card text-som-blue mr-2"></i>
                                    <span class="text-sm font-medium">Credit Card</span>
                                </label>
                                <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="payment_method" value="debit_card" class="mr-3">
                                    <i class="fas fa-credit-card text-som-green mr-2"></i>
                                    <span class="text-sm font-medium">Debit Card</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Cardholder Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Cardholder Name</label>
                            <input type="text" name="cardholder_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                   placeholder="Enter name as it appears on card">
                        </div>
                        
                        <!-- Card Number -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Card Number</label>
                            <input type="text" name="card_number" required maxlength="19"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                   placeholder="1234 5678 9012 3456"
                                   oninput="this.value = this.value.replace(/\s/g, '').replace(/(\d{4})/g, '$1 ').trim()">
                        </div>
                        
                        <!-- Expiry and CVV -->
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Expiry Date</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <select name="expiry_month" required class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                                        <option value="">Month</option>
                                        <?php for ($i = 1; $i <= 12; $i++): ?>
                                            <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                                        <?php endfor; ?>
                                    </select>
                                    <select name="expiry_year" required class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                                        <option value="">Year</option>
                                        <?php for ($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                                <input type="text" name="cvv" required maxlength="4"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                       placeholder="123">
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <button type="submit" class="w-full bg-som-green hover:bg-som-light-green text-white font-medium py-3 px-4 rounded-md transition-colors">
                            <i class="fas fa-lock mr-2"></i>Pay $<?php echo number_format($invoice['total_amount'], 2); ?>
                        </button>
                    </form>
                    
                    <div class="mt-6 text-center">
                        <p class="text-xs text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            This is a demo payment system. No actual charges will be made.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-som-border mt-12">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center text-sm text-gray-600">
                <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($company_info['company_name']); ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
