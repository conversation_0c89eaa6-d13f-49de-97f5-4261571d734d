<?php
// Som Milk Invoice Management System - Installation Script
// This script will create the database and tables

// Check if already installed
if (file_exists('includes/config.php')) {
    $config_content = file_get_contents('includes/config.php');
    if (strpos($config_content, 'INSTALLATION_COMPLETE') !== false) {
        die('System is already installed. If you need to reinstall, please delete the INSTALLATION_COMPLETE marker from config.php');
    }
}

$error = '';
$success = '';
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;

// Handle form submissions
if ($_POST) {
    if ($step == 1) {
        // Database connection test
        $host = $_POST['db_host'] ?? 'localhost';
        $username = $_POST['db_username'] ?? 'root';
        $password = $_POST['db_password'] ?? '';
        $database = $_POST['db_name'] ?? 'sommilk_invoice';
        
        try {
            // Test connection without database first
            $conn = new mysqli($host, $username, $password);

            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }

            // Create database if it doesn't exist
            $conn->query("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $conn->select_db($database);

            // Store connection details in session
            session_start();
            $_SESSION['install_db'] = [
                'host' => $host,
                'username' => $username,
                'password' => $password,
                'database' => $database
            ];

            $conn->close();
            header('Location: install.php?step=2');
            exit;

        } catch (Exception $e) {
            $error = "Database connection failed: " . $e->getMessage();
        }
    } elseif ($step == 2) {
        // Install database schema
        session_start();
        if (!isset($_SESSION['install_db'])) {
            header('Location: install.php?step=1');
            exit;
        }
        
        $db = $_SESSION['install_db'];
        
        try {
            $conn = new mysqli($db['host'], $db['username'], $db['password'], $db['database']);

            if ($conn->connect_error) {
                throw new Exception("Connection failed: " . $conn->connect_error);
            }

            // Read and execute schema
            $schema = file_get_contents('database/mysql_schema.sql');

            // Split by semicolon and execute each statement
            $statements = array_filter(array_map('trim', explode(';', $schema)));

            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^(--|\/\*|USE)/', $statement)) {
                    $conn->query($statement);
                }
            }

            // Update config file with database details
            $config_template = file_get_contents('includes/config.php');
            $config_template = str_replace('"localhost"', '"' . $db['host'] . '"', $config_template);
            $config_template = str_replace('"root"', '"' . $db['username'] . '"', $config_template);
            $config_template = str_replace('$password   = "";', '$password   = "' . $db['password'] . '";', $config_template);
            $config_template = str_replace('"sommilk_invoice"', '"' . $db['database'] . '"', $config_template);

            // Add installation complete marker
            $config_template .= "\n\n// Installation completed on " . date('Y-m-d H:i:s') . "\n// INSTALLATION_COMPLETE\n";

            file_put_contents('includes/config.php', $config_template);
            $conn->close();
            
            header('Location: install.php?step=3');
            exit;
            
        } catch (Exception $e) {
            $error = "Installation failed: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Som Milk Invoice Management - Installation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">Som Milk</h1>
                <h2 class="text-xl text-gray-600 mb-4">Invoice Management System</h2>
                <p class="text-sm text-gray-500">Installation Wizard</p>
            </div>

            <!-- Progress Steps -->
            <div class="flex justify-center mb-8">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full <?php echo $step >= 1 ? 'bg-som-blue text-white' : 'bg-gray-300 text-gray-600'; ?> flex items-center justify-center text-sm font-medium">
                            <?php echo $step > 1 ? '<i class="fas fa-check"></i>' : '1'; ?>
                        </div>
                        <span class="ml-2 text-sm text-gray-600">Database</span>
                    </div>
                    <div class="w-8 h-1 bg-gray-300 <?php echo $step >= 2 ? 'bg-som-blue' : ''; ?>"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full <?php echo $step >= 2 ? 'bg-som-blue text-white' : 'bg-gray-300 text-gray-600'; ?> flex items-center justify-center text-sm font-medium">
                            <?php echo $step > 2 ? '<i class="fas fa-check"></i>' : '2'; ?>
                        </div>
                        <span class="ml-2 text-sm text-gray-600">Install</span>
                    </div>
                    <div class="w-8 h-1 bg-gray-300 <?php echo $step >= 3 ? 'bg-som-blue' : ''; ?>"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full <?php echo $step >= 3 ? 'bg-som-blue text-white' : 'bg-gray-300 text-gray-600'; ?> flex items-center justify-center text-sm font-medium">
                            <?php echo $step > 3 ? '<i class="fas fa-check"></i>' : '3'; ?>
                        </div>
                        <span class="ml-2 text-sm text-gray-600">Complete</span>
                    </div>
                </div>
            </div>

            <!-- Error Message -->
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Success Message -->
            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- Installation Steps -->
            <div class="bg-white shadow-lg rounded-lg p-6">
                <?php if ($step == 1): ?>
                    <!-- Step 1: Database Configuration -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Database Configuration</h3>
                    <p class="text-gray-600 mb-6">Please enter your database connection details.</p>
                    
                    <form method="POST" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Database Host</label>
                            <input type="text" name="db_host" value="localhost" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Database Username</label>
                            <input type="text" name="db_username" value="root" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Database Password</label>
                            <input type="password" name="db_password"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Database Name</label>
                            <input type="text" name="db_name" value="sommilk_invoice" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        </div>
                        
                        <button type="submit" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-3 px-4 rounded-md transition duration-200">
                            Test Connection & Continue
                        </button>
                    </form>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Install Database -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Install Database</h3>
                    <p class="text-gray-600 mb-6">Ready to install the database schema and sample data.</p>
                    
                    <form method="POST">
                        <button type="submit" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-3 px-4 rounded-md transition duration-200">
                            <i class="fas fa-database mr-2"></i>
                            Install Database
                        </button>
                    </form>

                <?php elseif ($step == 3): ?>
                    <!-- Step 3: Installation Complete -->
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Installation Complete!</h3>
                    <p class="text-gray-600 mb-6">Your Som Milk Invoice Management System has been successfully installed.</p>
                    
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <h4 class="font-medium text-green-800 mb-2">What's Next?</h4>
                        <ul class="text-sm text-green-700 space-y-1">
                            <li>• Access your dashboard to start creating invoices</li>
                            <li>• Update company information in settings</li>
                            <li>• Upload your company logo</li>
                            <li>• Create your first invoice</li>
                        </ul>
                    </div>
                    
                    <div class="space-y-3">
                        <a href="index.php" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-3 px-4 rounded-md transition duration-200 flex items-center justify-center">
                            <i class="fas fa-home mr-2"></i>
                            Go to Dashboard
                        </a>
                        
                        <p class="text-xs text-gray-500 text-center">
                            For security, please delete the install.php file after installation.
                        </p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Footer -->
            <div class="text-center text-sm text-gray-500">
                Som Milk Invoice Management System v1.0.0
            </div>
        </div>
    </div>
</body>
</html>
