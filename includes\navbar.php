<nav class="bg-white shadow-lg border-b border-gray-200">
    <div class="container mx-auto px-4">
        <div class="flex justify-between items-center py-4">
            <!-- Logo and Brand -->
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <img src="assets/images/logo.png" alt="Som Milk Logo" class="h-10 w-10 mr-3" onerror="this.style.display='none'">
                    <div>
                        <h1 class="text-xl font-bold text-som-blue">Som Milk</h1>
                        <p class="text-xs text-gray-600">Invoice Management</p>
                    </div>
                </div>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-6">
                <a href="index.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                    <i class="fas fa-home mr-2"></i>Dashboard
                </a>
                <a href="create-invoice.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'create-invoice.php' ? 'active' : ''; ?>">
                    <i class="fas fa-plus mr-2"></i>Create Invoice
                </a>
                <a href="invoices.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'invoices.php' ? 'active' : ''; ?>">
                    <i class="fas fa-file-invoice mr-2"></i>All Invoices
                </a>
                <a href="customers.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'customers.php' ? 'active' : ''; ?>">
                    <i class="fas fa-users mr-2"></i>Customers
                </a>
                <a href="invoice-templates.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'invoice-templates.php' ? 'active' : ''; ?>">
                    <i class="fas fa-palette mr-2"></i>Templates
                </a>
                <a href="backup-export.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'backup-export.php' ? 'active' : ''; ?>">
                    <i class="fas fa-download mr-2"></i>Backup & Export
                </a>
                <a href="settings.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-cog mr-2"></i>Settings
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button id="mobile-menu-button" class="text-gray-600 hover:text-som-blue focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobile-menu" class="md:hidden hidden border-t border-gray-200 py-4">
            <div class="flex flex-col space-y-3">
                <a href="index.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                    <i class="fas fa-home mr-3"></i>Dashboard
                </a>
                <a href="create-invoice.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'create-invoice.php' ? 'active' : ''; ?>">
                    <i class="fas fa-plus mr-3"></i>Create Invoice
                </a>
                <a href="invoices.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'invoices.php' ? 'active' : ''; ?>">
                    <i class="fas fa-file-invoice mr-3"></i>All Invoices
                </a>
                <a href="customers.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'customers.php' ? 'active' : ''; ?>">
                    <i class="fas fa-users mr-3"></i>Customers
                </a>
                <a href="invoice-templates.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'invoice-templates.php' ? 'active' : ''; ?>">
                    <i class="fas fa-palette mr-3"></i>Templates
                </a>
                <a href="backup-export.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'backup-export.php' ? 'active' : ''; ?>">
                    <i class="fas fa-download mr-3"></i>Backup & Export
                </a>
                <a href="settings.php" class="mobile-nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'settings.php' ? 'active' : ''; ?>">
                    <i class="fas fa-cog mr-3"></i>Settings
                </a>
            </div>
        </div>
    </div>
</nav>

<style>
.nav-link {
    @apply text-gray-600 hover:text-som-blue font-medium transition duration-200 flex items-center px-3 py-2 rounded-md;
}

.nav-link.active {
    @apply text-som-blue bg-blue-50;
}

.mobile-nav-link {
    @apply text-gray-600 hover:text-som-blue font-medium transition duration-200 flex items-center px-3 py-2 rounded-md;
}

.mobile-nav-link.active {
    @apply text-som-blue bg-blue-50;
}
</style>

<script>
// Mobile menu toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
});
</script>
