<?php
require_once 'includes/config.php';

echo "Simple Dashboard Test\n";
echo "=====================\n\n";

// Basic database queries
echo "Database Statistics:\n";
echo "--------------------\n";

$queries = [
    'invoices' => "SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM invoices",
    'customers' => "SELECT COUNT(*) as count FROM customers WHERE status = 'active'",
    'products' => "SELECT COUNT(*) as count FROM products WHERE active = 1",
    'users' => "SELECT COUNT(*) as count FROM users WHERE status = 'active'"
];

foreach ($queries as $name => $query) {
    $result = $conn->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        if ($name === 'invoices') {
            echo "✅ $name: " . $row['count'] . " (Total: $" . number_format($row['total'], 2) . ")\n";
        } else {
            echo "✅ $name: " . $row['count'] . "\n";
        }
    } else {
        echo "❌ $name: Error - " . $conn->error . "\n";
    }
}

echo "\n✅ Simple dashboard test completed!\n";
?>
