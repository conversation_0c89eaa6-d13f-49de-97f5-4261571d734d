// Main JavaScript file for Som Milk Invoice Management System

// Global variables
let invoiceItems = [];
let itemCounter = 0;

// Document ready
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    // Initialize mobile menu
    initializeMobileMenu();
    
    // Initialize invoice form if present
    if (document.getElementById('invoice-form')) {
        initializeInvoiceForm();
    }
    
    // Initialize search functionality
    initializeSearch();
    
    // Initialize tooltips and other UI components
    initializeUIComponents();
}

// Mobile menu functionality
function initializeMobileMenu() {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!mobileMenuButton.contains(event.target) && !mobileMenu.contains(event.target)) {
                mobileMenu.classList.add('hidden');
            }
        });
    }
}

// Invoice form functionality
function initializeInvoiceForm() {
    // Add first item row
    addInvoiceItem();
    
    // Initialize form validation
    const form = document.getElementById('invoice-form');
    if (form) {
        form.addEventListener('submit', validateInvoiceForm);
    }
    
    // Initialize date fields with today's date
    const invoiceDateField = document.getElementById('invoice_date');
    const dueDateField = document.getElementById('due_date');
    
    if (invoiceDateField && !invoiceDateField.value) {
        invoiceDateField.value = new Date().toISOString().split('T')[0];
    }
    
    if (dueDateField && !dueDateField.value) {
        const dueDate = new Date();
        dueDate.setDate(dueDate.getDate() + 30); // 30 days from today
        dueDateField.value = dueDate.toISOString().split('T')[0];
    }
}

// Add invoice item row
function addInvoiceItem() {
    itemCounter++;
    const itemsContainer = document.getElementById('invoice-items');
    
    const itemRow = document.createElement('div');
    itemRow.className = 'invoice-item grid grid-cols-1 md:grid-cols-12 gap-4 p-4 bg-gray-50 rounded-lg';
    itemRow.id = `item-${itemCounter}`;
    
    itemRow.innerHTML = `
        <div class="md:col-span-5">
            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <input type="text" name="items[${itemCounter}][description]" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                   placeholder="Item description" required>
        </div>
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
            <input type="number" name="items[${itemCounter}][quantity]" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                   placeholder="1" min="1" step="1" value="1" 
                   onchange="calculateItemTotal(${itemCounter})" required>
        </div>
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">Unit Price ($)</label>
            <input type="number" name="items[${itemCounter}][unit_price]" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                   placeholder="0.00" min="0" step="0.01" 
                   onchange="calculateItemTotal(${itemCounter})" required>
        </div>
        <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-1">Total ($)</label>
            <input type="number" name="items[${itemCounter}][total_price]" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                   placeholder="0.00" readonly>
        </div>
        <div class="md:col-span-1 flex items-end">
            <button type="button" onclick="removeInvoiceItem(${itemCounter})" 
                    class="w-full px-3 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition duration-200">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    itemsContainer.appendChild(itemRow);
    invoiceItems.push(itemCounter);
    
    // Focus on description field
    const descriptionField = itemRow.querySelector('input[type="text"]');
    if (descriptionField) {
        descriptionField.focus();
    }
}

// Remove invoice item
function removeInvoiceItem(itemId) {
    const itemRow = document.getElementById(`item-${itemId}`);
    if (itemRow && invoiceItems.length > 1) {
        itemRow.remove();
        invoiceItems = invoiceItems.filter(id => id !== itemId);
        calculateInvoiceTotal();
    } else if (invoiceItems.length === 1) {
        showNotification('At least one item is required', 'warning');
    }
}

// Calculate item total
function calculateItemTotal(itemId) {
    const quantityField = document.querySelector(`input[name="items[${itemId}][quantity]"]`);
    const unitPriceField = document.querySelector(`input[name="items[${itemId}][unit_price]"]`);
    const totalField = document.querySelector(`input[name="items[${itemId}][total_price]"]`);
    
    if (quantityField && unitPriceField && totalField) {
        const quantity = parseFloat(quantityField.value) || 0;
        const unitPrice = parseFloat(unitPriceField.value) || 0;
        const total = quantity * unitPrice;
        
        totalField.value = total.toFixed(2);
        calculateInvoiceTotal();
    }
}

// Calculate invoice total
function calculateInvoiceTotal() {
    let subtotal = 0;
    
    // Sum all item totals
    invoiceItems.forEach(itemId => {
        const totalField = document.querySelector(`input[name="items[${itemId}][total_price]"]`);
        if (totalField) {
            subtotal += parseFloat(totalField.value) || 0;
        }
    });
    
    // Get tax rate (default 0%)
    const taxRateField = document.getElementById('tax_rate');
    const taxRate = taxRateField ? parseFloat(taxRateField.value) || 0 : 0;
    
    // Calculate tax and total
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;
    
    // Update display fields
    const subtotalField = document.getElementById('subtotal');
    const taxAmountField = document.getElementById('tax_amount');
    const totalField = document.getElementById('total_amount');
    
    if (subtotalField) subtotalField.value = subtotal.toFixed(2);
    if (taxAmountField) taxAmountField.value = taxAmount.toFixed(2);
    if (totalField) totalField.value = total.toFixed(2);
    
    // Update display elements
    const subtotalDisplay = document.getElementById('subtotal-display');
    const taxDisplay = document.getElementById('tax-display');
    const totalDisplay = document.getElementById('total-display');
    
    if (subtotalDisplay) subtotalDisplay.textContent = '$' + subtotal.toFixed(2);
    if (taxDisplay) taxDisplay.textContent = '$' + taxAmount.toFixed(2);
    if (totalDisplay) totalDisplay.textContent = '$' + total.toFixed(2);
}

// Validate invoice form
function validateInvoiceForm(event) {
    const form = event.target;
    let isValid = true;
    let errorMessage = '';
    
    // Check if at least one item exists
    if (invoiceItems.length === 0) {
        isValid = false;
        errorMessage = 'At least one item is required.';
    }
    
    // Validate each item
    invoiceItems.forEach(itemId => {
        const descriptionField = document.querySelector(`input[name="items[${itemId}][description]"]`);
        const quantityField = document.querySelector(`input[name="items[${itemId}][quantity]"]`);
        const unitPriceField = document.querySelector(`input[name="items[${itemId}][unit_price]"]`);
        
        if (!descriptionField.value.trim()) {
            isValid = false;
            errorMessage = 'All items must have a description.';
        }
        
        if (!quantityField.value || parseFloat(quantityField.value) <= 0) {
            isValid = false;
            errorMessage = 'All items must have a valid quantity.';
        }
        
        if (!unitPriceField.value || parseFloat(unitPriceField.value) < 0) {
            isValid = false;
            errorMessage = 'All items must have a valid unit price.';
        }
    });
    
    if (!isValid) {
        event.preventDefault();
        showNotification(errorMessage, 'error');
        return false;
    }
    
    return true;
}

// Search functionality
function initializeSearch() {
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(performSearch, 300));
    }
}

// Perform search
function performSearch() {
    const searchInput = document.getElementById('search-input');
    const searchTerm = searchInput.value.trim();
    
    // Redirect to invoices page with search parameter
    if (searchTerm) {
        window.location.href = `invoices.php?search=${encodeURIComponent(searchTerm)}`;
    } else {
        window.location.href = 'invoices.php';
    }
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;
    
    // Set notification style based on type
    switch (type) {
        case 'success':
            notification.className += ' bg-green-500 text-white';
            break;
        case 'error':
            notification.className += ' bg-red-500 text-white';
            break;
        case 'warning':
            notification.className += ' bg-yellow-500 text-white';
            break;
        default:
            notification.className += ' bg-blue-500 text-white';
    }
    
    notification.innerHTML = `
        <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Initialize UI components
function initializeUIComponents() {
    // Initialize any tooltips, modals, or other UI components here
    
    // Add loading states to forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
            }
        });
    });
}

// Utility functions
function formatCurrency(amount) {
    return '$' + parseFloat(amount).toFixed(2);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Export functions for global use
window.addInvoiceItem = addInvoiceItem;
window.removeInvoiceItem = removeInvoiceItem;
window.calculateItemTotal = calculateItemTotal;
window.calculateInvoiceTotal = calculateInvoiceTotal;
window.showNotification = showNotification;
