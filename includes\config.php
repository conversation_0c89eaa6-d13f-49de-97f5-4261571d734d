<?php
// Database connection settings
$servername = "localhost";
$username   = "root";
$password   = "";
$dbname     = "sommilk_invoice";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// If connected successfully
// echo "Connected successfully";

// Application Configuration
define('APP_NAME', 'Som Milk Invoice Management');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/sommilk_invoice/');

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('INVOICE_UPLOAD_DIR', 'uploads/invoices/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Invoice Configuration
define('INVOICE_PREFIX', 'SM-');
define('DEFAULT_CURRENCY', 'USD');
define('DEFAULT_TAX_RATE', 0.00); // 0% tax by default

// Company Information (can be overridden by database)
define('COMPANY_NAME', 'Som Milk');
define('COMPANY_ADDRESS', '123 Dairy Street');
define('COMPANY_CITY', 'Milk City');
define('COMPANY_STATE', 'MC');
define('COMPANY_ZIP', '12345');
define('COMPANY_PHONE', '+****************');
define('COMPANY_EMAIL', '<EMAIL>');



// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('America/New_York');



// Helper function to get base URL
function getBaseUrl() {
    return BASE_URL;
}

// Helper function to redirect
function redirect($url) {
    header("Location: " . $url);
    exit();
}
?>
