<?php
// Session Configuration (must be set before session_start())
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'sommilk_invoice');

// Application Configuration
define('APP_NAME', 'Som Milk Invoice Management');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/sommilk_invoice/');

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('INVOICE_UPLOAD_DIR', 'uploads/invoices/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Invoice Configuration
define('INVOICE_PREFIX', 'SM-');
define('DEFAULT_CURRENCY', 'USD');
define('DEFAULT_TAX_RATE', 0.00); // 0% tax by default

// Company Information (can be overridden by database)
define('COMPANY_NAME', 'Som Milk');
define('COMPANY_ADDRESS', '123 Dairy Street');
define('COMPANY_CITY', 'Milk City');
define('COMPANY_STATE', 'MC');
define('COMPANY_ZIP', '12345');
define('COMPANY_PHONE', '+****************');
define('COMPANY_EMAIL', '<EMAIL>');

// Database Connection
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USERNAME,
        DB_PASSWORD,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException $e) {
    // Log error and show user-friendly message
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please check your configuration.");
}

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('America/New_York');



// Helper function to get base URL
function getBaseUrl() {
    return BASE_URL;
}

// Helper function to redirect
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// Helper function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Helper function to format currency
function formatCurrency($amount, $currency = DEFAULT_CURRENCY) {
    return '$' . number_format($amount, 2);
}

// Helper function to generate invoice number
function generateInvoiceNumber() {
    global $pdo;
    
    // Get the last invoice number
    $stmt = $pdo->query("SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1");
    $lastInvoice = $stmt->fetch();
    
    if ($lastInvoice) {
        // Extract number from last invoice (e.g., SM-001 -> 001)
        $lastNumber = (int) substr($lastInvoice['invoice_number'], strlen(INVOICE_PREFIX));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return INVOICE_PREFIX . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
}

// Helper function to validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Helper function to validate phone
function isValidPhone($phone) {
    return preg_match('/^[\+]?[1-9][\d]{0,15}$/', $phone);
}
?>
