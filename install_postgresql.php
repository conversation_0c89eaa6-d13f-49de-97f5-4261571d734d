<?php
/**
 * Som Milk Invoice Management System
 * PostgreSQL Installation Script for Replit
 */

// Check if already installed
if (file_exists('includes/config.php') && !isset($_GET['force'])) {
    $config_content = file_get_contents('includes/config.php');
    if (strpos($config_content, 'pgsql:') !== false) {
        header('Location: index.php');
        exit;
    }
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_POST) {
    switch ($step) {
        case 1:
            // Database connection test
            $host = $_POST['db_host'] ?? '';
            $port = $_POST['db_port'] ?? '5432';
            $dbname = $_POST['db_name'] ?? '';
            $username = $_POST['db_username'] ?? '';
            $password = $_POST['db_password'] ?? '';
            
            try {
                $pdo = new PDO(
                    "pgsql:host=$host;port=$port;dbname=$dbname",
                    $username,
                    $password,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                
                // Store connection details in session
                session_start();
                $_SESSION['db_config'] = [
                    'host' => $host,
                    'port' => $port,
                    'dbname' => $dbname,
                    'username' => $username,
                    'password' => $password
                ];
                
                header('Location: ?step=2');
                exit;
            } catch (PDOException $e) {
                $error = "Database connection failed: " . $e->getMessage();
            }
            break;
            
        case 2:
            // Create database schema
            session_start();
            $config = $_SESSION['db_config'];
            
            try {
                $pdo = new PDO(
                    "pgsql:host={$config['host']};port={$config['port']};dbname={$config['dbname']}",
                    $config['username'],
                    $config['password'],
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
                
                // Read and execute schema
                $schema = file_get_contents('database/postgresql_schema.sql');
                $pdo->exec($schema);
                
                // Create config file
                $config_content = "<?php
// Database Configuration for PostgreSQL
define('DB_HOST', '{$config['host']}');
define('DB_USERNAME', '{$config['username']}');
define('DB_PASSWORD', '{$config['password']}');
define('DB_NAME', '{$config['dbname']}');
define('DB_PORT', '{$config['port']}');

// Application Configuration
define('APP_NAME', 'Som Milk Invoice Management');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/sommilk_invoice/');

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('INVOICE_UPLOAD_DIR', 'uploads/invoices/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Invoice Configuration
define('INVOICE_PREFIX', 'SM-');
define('DEFAULT_CURRENCY', 'USD');
define('DEFAULT_TAX_RATE', 0.00); // 0% tax by default

// Company Information (can be overridden by database)
define('COMPANY_NAME', 'Som Milk');
define('COMPANY_ADDRESS', '123 Dairy Street');
define('COMPANY_CITY', 'Milk City');
define('COMPANY_STATE', 'MC');
define('COMPANY_ZIP', '12345');
define('COMPANY_PHONE', '+****************');
define('COMPANY_EMAIL', '<EMAIL>');

// Database Connection
try {
    \$pdo = new PDO(
        \"pgsql:host=\" . DB_HOST . \";port=\" . DB_PORT . \";dbname=\" . DB_NAME,
        DB_USERNAME,
        DB_PASSWORD,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch (PDOException \$e) {
    // Log error and show user-friendly message
    error_log(\"Database connection failed: \" . \$e->getMessage());
    die(\"Database connection failed. Please check your configuration.\");
}

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('America/New_York');

// Helper function to get base URL
function getBaseUrl() {
    return BASE_URL;
}

// Helper function to format currency
function formatCurrency(\$amount) {
    return '$' . number_format(\$amount, 2);
}

// Helper function to format date
function formatDate(\$date, \$format = 'M j, Y') {
    return date(\$format, strtotime(\$date));
}
?>";
                
                file_put_contents('includes/config.php', $config_content);
                
                // Create uploads directory
                if (!is_dir('uploads')) {
                    mkdir('uploads', 0755, true);
                }
                if (!is_dir('uploads/invoices')) {
                    mkdir('uploads/invoices', 0755, true);
                }
                
                header('Location: ?step=3');
                exit;
            } catch (Exception $e) {
                $error = "Installation failed: " . $e->getMessage();
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Som Milk Invoice System - PostgreSQL Installation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#2563EB',
                        'som-green': '#059669',
                        'som-amber': '#F59E0B'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-900">Som Milk</h1>
                <h2 class="mt-2 text-xl text-gray-600">Invoice Management System</h2>
                <p class="mt-2 text-sm text-gray-500">PostgreSQL Installation</p>
            </div>

            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <!-- Step 1: Database Configuration -->
                <div class="bg-white shadow-md rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Step 1: Database Configuration</h3>
                    <form method="POST">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Host</label>
                                <input type="text" name="db_host" value="localhost" required
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-som-blue focus:border-som-blue">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Port</label>
                                <input type="text" name="db_port" value="5432" required
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-som-blue focus:border-som-blue">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Database Name</label>
                                <input type="text" name="db_name" value="sommilk_invoice" required
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-som-blue focus:border-som-blue">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Username</label>
                                <input type="text" name="db_username" value="postgres" required
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-som-blue focus:border-som-blue">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Password</label>
                                <input type="password" name="db_password"
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-som-blue focus:border-som-blue">
                            </div>
                        </div>
                        <button type="submit" class="mt-6 w-full bg-som-blue text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-som-blue">
                            Test Connection
                        </button>
                    </form>
                </div>

            <?php elseif ($step == 2): ?>
                <!-- Step 2: Install Database -->
                <div class="bg-white shadow-md rounded-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Step 2: Install Database Schema</h3>
                    <p class="text-gray-600 mb-4">Click the button below to create the database tables and initial data.</p>
                    <form method="POST">
                        <button type="submit" class="w-full bg-som-green text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-som-green">
                            Install Database
                        </button>
                    </form>
                </div>

            <?php elseif ($step == 3): ?>
                <!-- Step 3: Complete -->
                <div class="bg-white shadow-md rounded-lg p-6 text-center">
                    <div class="text-som-green text-6xl mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-4">Installation Complete!</h3>
                    <p class="text-gray-600 mb-6">Your Som Milk Invoice Management System has been successfully installed with PostgreSQL.</p>
                    <a href="index.php" class="bg-som-blue text-white py-2 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-som-blue">
                        Go to Dashboard
                    </a>
                </div>
            <?php endif; ?>

            <div class="text-center text-sm text-gray-500">
                <p>Som Milk Invoice Management System v1.0</p>
                <p>PostgreSQL Edition</p>
            </div>
        </div>
    </div>
</body>
</html>
