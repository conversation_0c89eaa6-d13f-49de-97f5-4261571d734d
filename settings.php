<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get company information
$company_info = getCompanyInfo();

// Handle form submission
if ($_POST && isset($_POST['update_company'])) {
    $errors = [];

    // Validate required fields
    if (empty($_POST['company_name'])) {
        $errors[] = "Company name is required.";
    }

    // Handle logo upload
    $logoPath = $company_info['logo_path'] ?? '';

    // Remove logo if requested
    if (isset($_POST['remove_logo']) && $_POST['remove_logo'] == '1') {
        if (!empty($logoPath) && file_exists($logoPath)) {
            unlink($logoPath);
        }
        $logoPath = '';
    }

    // Process new logo upload
    if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/logos/';

        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $fileInfo = pathinfo($_FILES['company_logo']['name']);
        $extension = strtolower($fileInfo['extension']);

        // Validate file type
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
        if (!in_array($extension, $allowedTypes)) {
            $errors[] = "Logo must be a JPG, PNG, or GIF file.";
        }

        // Validate file size (2MB max)
        if ($_FILES['company_logo']['size'] > 2 * 1024 * 1024) {
            $errors[] = "Logo file size must be less than 2MB.";
        }

        if (empty($errors)) {
            // Remove old logo
            if (!empty($logoPath) && file_exists($logoPath)) {
                unlink($logoPath);
            }

            // Generate unique filename
            $filename = 'logo_' . time() . '.' . $extension;
            $logoPath = $uploadDir . $filename;

            // Move uploaded file
            if (!move_uploaded_file($_FILES['company_logo']['tmp_name'], $logoPath)) {
                $errors[] = "Failed to upload logo file.";
                $logoPath = $company_info['logo_path'] ?? '';
            }
        }
    }

    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("
                UPDATE company_info SET
                    company_name = ?, address = ?, city = ?, state = ?, zip_code = ?,
                    phone = ?, email = ?, website = ?,
                    bank_name = ?, account_number = ?, routing_number = ?, tax_id = ?, logo_path = ?
                WHERE id = 1
            ");

            $company_name = sanitizeInput($_POST['company_name']);
            $address = sanitizeInput($_POST['address']);
            $city = sanitizeInput($_POST['city']);
            $state = sanitizeInput($_POST['state']);
            $zip_code = sanitizeInput($_POST['zip_code']);
            $phone = sanitizeInput($_POST['phone']);
            $email = sanitizeInput($_POST['email']);
            $website = sanitizeInput($_POST['website']);
            $bank_name = sanitizeInput($_POST['bank_name']);
            $account_number = sanitizeInput($_POST['account_number']);
            $routing_number = sanitizeInput($_POST['routing_number']);
            $tax_id = sanitizeInput($_POST['tax_id']);

            $stmt->bind_param("sssssssssssss",
                $company_name, $address, $city, $state, $zip_code,
                $phone, $email, $website, $bank_name, $account_number,
                $routing_number, $tax_id, $logoPath
            );

            $result = $stmt->execute();

            if ($result) {
                $_SESSION['success_message'] = "Company information updated successfully!";
                // Refresh company info
                $company_info = getCompanyInfo();
            } else {
                $errors[] = "Failed to update company information.";
            }

        } catch (mysqli_sql_exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Som Milk Invoice Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
            <p class="text-gray-600">Manage your company information and system settings</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Please fix the following errors:</strong>
                </div>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form method="POST" enctype="multipart/form-data" class="space-y-8">
            <input type="hidden" name="update_company" value="1">

            <!-- Company Logo -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Company Logo</h2>

                <div class="flex items-start space-x-6">
                    <!-- Current Logo Display -->
                    <div class="flex-shrink-0">
                        <?php if (!empty($company_info['logo_path']) && file_exists($company_info['logo_path'])): ?>
                            <img src="<?php echo htmlspecialchars($company_info['logo_path']); ?>"
                                 alt="Company Logo" class="w-32 h-32 object-contain border border-gray-300 rounded-lg">
                        <?php else: ?>
                            <div class="w-32 h-32 bg-gray-100 border border-gray-300 rounded-lg flex items-center justify-center">
                                <i class="fas fa-building text-3xl text-gray-400"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Logo Upload -->
                    <div class="flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Upload New Logo</label>
                        <input type="file" name="company_logo" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        <p class="text-sm text-gray-500 mt-1">Recommended: PNG or JPG, max 2MB, square format works best</p>

                        <?php if (!empty($company_info['logo_path'])): ?>
                            <div class="mt-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="remove_logo" value="1" class="mr-2">
                                    <span class="text-sm text-red-600">Remove current logo</span>
                                </label>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Company Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Company Name -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Company Name *</label>
                        <input type="text" name="company_name" value="<?php echo htmlspecialchars($company_info['company_name']); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Address -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <textarea name="address" rows="2"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"><?php echo htmlspecialchars($company_info['address']); ?></textarea>
                    </div>
                    
                    <!-- City -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                        <input type="text" name="city" value="<?php echo htmlspecialchars($company_info['city']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- State -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                        <input type="text" name="state" value="<?php echo htmlspecialchars($company_info['state']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- ZIP Code -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">ZIP Code</label>
                        <input type="text" name="zip_code" value="<?php echo htmlspecialchars($company_info['zip_code']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Phone -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input type="tel" name="phone" value="<?php echo htmlspecialchars($company_info['phone']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Email -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="email" value="<?php echo htmlspecialchars($company_info['email']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Website -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                        <input type="url" name="website" value="<?php echo htmlspecialchars($company_info['website']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Banking Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Banking Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Bank Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                        <input type="text" name="bank_name" value="<?php echo htmlspecialchars($company_info['bank_name']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Account Holder Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Account Holder Name</label>
                        <input type="text" name="account_holder_name" value="<?php echo htmlspecialchars($company_info['account_holder_name']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Account Number -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                        <input type="text" name="account_number" value="<?php echo htmlspecialchars($company_info['account_number']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Routing Number -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Routing Number</label>
                        <input type="text" name="routing_number" value="<?php echo htmlspecialchars($company_info['routing_number']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                </div>
            </div>

            <!-- Invoice Settings -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Invoice Settings</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Invoice Prefix -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Invoice Prefix</label>
                        <input type="text" name="invoice_prefix" value="<?php echo htmlspecialchars($company_info['invoice_prefix']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                               placeholder="SM-">
                    </div>
                    
                    <!-- Default Tax Rate -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Default Tax Rate (%)</label>
                        <input type="number" name="tax_rate" value="<?php echo $company_info['tax_rate']; ?>" min="0" max="100" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Invoice Terms -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Default Invoice Terms</label>
                        <textarea name="invoice_terms" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                  placeholder="Payment is due within 30 days..."><?php echo htmlspecialchars($company_info['invoice_terms']); ?></textarea>
                    </div>
                    
                    <!-- Invoice Notes -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Default Invoice Notes</label>
                        <textarea name="invoice_notes" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                  placeholder="Thank you for your business..."><?php echo htmlspecialchars($company_info['invoice_notes']); ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end">
                <button type="submit" class="px-6 py-3 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>Save Settings
                </button>
            </div>
        </form>

        <!-- System Information -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">System Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div>
                    <div class="text-gray-600">Application Version</div>
                    <div class="font-medium">1.0.0</div>
                </div>
                <div>
                    <div class="text-gray-600">PHP Version</div>
                    <div class="font-medium"><?php echo PHP_VERSION; ?></div>
                </div>
                <div>
                    <div class="text-gray-600">Database</div>
                    <div class="font-medium">MySQL</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
</body>
</html>
