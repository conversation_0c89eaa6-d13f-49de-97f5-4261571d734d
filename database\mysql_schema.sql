-- Som Milk Invoice Management System
-- MySQL Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS sommilk_invoice;
USE sommilk_invoice;

-- Company information table
CREATE TABLE IF NOT EXISTS company_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL DEFAULT 'Som Milk',
    address VARCHAR(255) DEFAULT '123 Dairy Street',
    city VARCHAR(100) DEFAULT 'Milk City',
    state VARCHAR(50) DEFAULT 'MC',
    zip_code VARCHAR(20) DEFAULT '12345',
    phone VARCHAR(20) DEFAULT '+****************',
    email VARCHAR(255) DEFAULT '<EMAIL>',
    website VARCHAR(255) DEFAULT '',
    logo_path VARCHAR(255) DEFAULT '',
    invoice_template INT DEFAULT 1,
    bank_name VARCHAR(255) DEFAULT '',
    account_number VARCHAR(100) DEFAULT '',
    routing_number VARCHAR(100) DEFAULT '',
    tax_id VARCHAR(50) DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) DEFAULT '',
    phone VARCHAR(20) DEFAULT '',
    address VARCHAR(255) DEFAULT '',
    city VARCHAR(100) DEFAULT '',
    state VARCHAR(50) DEFAULT '',
    zip_code VARCHAR(20) DEFAULT '',
    company VARCHAR(255) DEFAULT '',
    notes TEXT DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer_name (name),
    INDEX idx_customer_email (email)
);

-- Invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT DEFAULT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) DEFAULT '',
    customer_phone VARCHAR(20) DEFAULT '',
    customer_address VARCHAR(255) DEFAULT '',
    customer_city VARCHAR(100) DEFAULT '',
    customer_state VARCHAR(50) DEFAULT '',
    customer_zip VARCHAR(20) DEFAULT '',
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    notes TEXT DEFAULT '',
    terms TEXT DEFAULT '',
    payment_method VARCHAR(50) DEFAULT '',
    payment_date DATE DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_name (customer_name),
    INDEX idx_status (status),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_due_date (due_date)
);

-- Invoice items table
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
);

-- Products/Services table (optional for future use)
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT DEFAULT '',
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    category VARCHAR(100) DEFAULT '',
    sku VARCHAR(100) DEFAULT '',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_product_name (name),
    INDEX idx_sku (sku),
    INDEX idx_category (category)
);

-- Payment records table (optional for future use)
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) DEFAULT '',
    reference_number VARCHAR(100) DEFAULT '',
    notes TEXT DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_payment_date (payment_date)
);

-- Triggers for automatic calculations
DELIMITER //

-- Trigger to update invoice totals when items are inserted
CREATE TRIGGER update_invoice_totals_insert
AFTER INSERT ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = NEW.invoice_id
    ),
    tax_amount = subtotal * tax_rate,
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = NEW.invoice_id;
END//

-- Trigger to update invoice totals when items are updated
CREATE TRIGGER update_invoice_totals_update
AFTER UPDATE ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = NEW.invoice_id
    ),
    tax_amount = subtotal * tax_rate,
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = NEW.invoice_id;
END//

-- Trigger to update invoice totals when items are deleted
CREATE TRIGGER update_invoice_totals_delete
AFTER DELETE ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = OLD.invoice_id
    ),
    tax_amount = subtotal * tax_rate,
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = OLD.invoice_id;
END//

DELIMITER ;

-- Insert default company information
INSERT INTO company_info (
    company_name, address, city, state, zip_code, phone, email
) VALUES (
    'Som Milk', '123 Dairy Street', 'Milk City', 'MC', '12345', 
    '+****************', '<EMAIL>'
) ON DUPLICATE KEY UPDATE id=id;

-- Insert sample customers
INSERT INTO customers (name, email, phone, address, city, state, zip_code, company) VALUES
('John Smith', '<EMAIL>', '+****************', '123 Main St', 'Anytown', 'ST', '12345', 'Smith Corp'),
('Jane Doe', '<EMAIL>', '+****************', '456 Oak Ave', 'Somewhere', 'ST', '67890', 'Doe Industries'),
('Bob Johnson', '<EMAIL>', '+****************', '789 Pine Rd', 'Elsewhere', 'ST', '11111', 'Johnson LLC')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample invoices
INSERT INTO invoices (
    invoice_number, customer_name, customer_email, customer_address, 
    customer_city, customer_state, customer_zip, invoice_date, due_date,
    subtotal, tax_rate, tax_amount, total_amount, status, notes
) VALUES
('SM-1001', 'John Smith', '<EMAIL>', '123 Main St', 'Anytown', 'ST', '12345', 
 CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 1000.00, 0.0000, 0.00, 1000.00, 'pending', 'Sample invoice 1'),
('SM-1002', 'Jane Doe', '<EMAIL>', '456 Oak Ave', 'Somewhere', 'ST', '67890', 
 CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 1500.00, 0.0000, 0.00, 1500.00, 'paid', 'Sample invoice 2')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample invoice items
INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price) VALUES
(1, 'Premium Milk - 1 Gallon', 10.00, 50.00, 500.00),
(1, 'Organic Cheese - 1 lb', 20.00, 25.00, 500.00),
(2, 'Fresh Butter - 1 lb', 15.00, 40.00, 600.00),
(2, 'Yogurt - 32 oz', 30.00, 30.00, 900.00)
ON DUPLICATE KEY UPDATE id=id;

-- Invoice Status Log Table
CREATE TABLE invoice_status_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    old_status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') NOT NULL,
    new_status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_invoices_created_at ON invoices(created_at);
CREATE INDEX idx_invoices_updated_at ON invoices(updated_at);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_due_date ON invoices(due_date);
CREATE INDEX idx_customers_created_at ON customers(created_at);
CREATE INDEX idx_invoice_items_created_at ON invoice_items(created_at);
CREATE INDEX idx_invoice_status_log_invoice_id ON invoice_status_log(invoice_id);
CREATE INDEX idx_invoice_status_log_created_at ON invoice_status_log(created_at);
