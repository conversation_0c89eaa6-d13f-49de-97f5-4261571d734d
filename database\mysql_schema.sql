-- Som Milk Invoice Management System
-- MySQL Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS sommilk_invoice;
USE sommilk_invoice;

-- Company information table
CREATE TABLE IF NOT EXISTS company_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL DEFAULT 'Som Milk',
    address VARCHAR(255) DEFAULT '123 Dairy Street',
    city VARCHAR(100) DEFAULT 'Milk City',
    state VARCHAR(50) DEFAULT 'MC',
    zip_code VARCHAR(20) DEFAULT '12345',
    phone VARCHAR(20) DEFAULT '+****************',
    email VARCHAR(255) DEFAULT '<EMAIL>',
    website VARCHAR(255) DEFAULT '',
    logo_path VARCHAR(255) DEFAULT '',
    invoice_template INT DEFAULT 1,
    bank_name VARCHAR(255) DEFAULT '',
    account_number VARCHAR(100) DEFAULT '',
    routing_number VARCHAR(100) DEFAULT '',
    tax_id VARCHAR(50) DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Admin Users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role ENUM('admin', 'manager', 'user') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Som Milk Administrator', 'admin')
ON DUPLICATE KEY UPDATE username = username;

-- General Users table (for customer portal, staff, etc.)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    full_name VARCHAR(255) GENERATED ALWAYS AS (CONCAT(first_name, ' ', last_name)) STORED,
    phone VARCHAR(20) DEFAULT '',

    -- User Type and Permissions
    user_type ENUM('customer', 'staff', 'contractor', 'vendor') DEFAULT 'customer',
    status ENUM('active', 'inactive', 'pending', 'suspended') DEFAULT 'pending',

    -- Profile Information
    avatar_path VARCHAR(255) DEFAULT '',
    date_of_birth DATE NULL,
    gender ENUM('male', 'female', 'other', 'prefer_not_to_say') DEFAULT 'prefer_not_to_say',

    -- Address Information
    address_line1 VARCHAR(255) DEFAULT '',
    address_line2 VARCHAR(255) DEFAULT '',
    city VARCHAR(100) DEFAULT '',
    state VARCHAR(50) DEFAULT '',
    zip_code VARCHAR(20) DEFAULT '',
    country VARCHAR(100) DEFAULT 'United States',

    -- Company/Organization (for staff/contractors)
    company_name VARCHAR(255) DEFAULT '',
    job_title VARCHAR(100) DEFAULT '',
    department VARCHAR(100) DEFAULT '',

    -- Customer-specific fields
    customer_id INT NULL,
    customer_portal_access BOOLEAN DEFAULT FALSE,

    -- Security and Access
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255) DEFAULT '',
    phone_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255) DEFAULT '',

    -- Login Security
    last_login TIMESTAMP NULL,
    last_login_ip VARCHAR(45) DEFAULT '',
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    password_reset_token VARCHAR(255) DEFAULT '',
    password_reset_expires TIMESTAMP NULL,

    -- Preferences
    timezone VARCHAR(50) DEFAULT 'America/New_York',
    language VARCHAR(10) DEFAULT 'en',
    notification_preferences JSON DEFAULT '{}',

    -- Metadata
    created_by INT DEFAULT NULL,
    notes TEXT DEFAULT '',
    tags VARCHAR(500) DEFAULT '',

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- Foreign Keys
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES admin_users(id) ON DELETE SET NULL,

    -- Indexes
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_status (status),
    INDEX idx_customer_id (customer_id),
    INDEX idx_full_name (first_name, last_name),
    INDEX idx_company (company_name),
    INDEX idx_created_at (created_at),
    INDEX idx_last_login (last_login),
    INDEX idx_email_verified (email_verified),
    INDEX idx_customer_portal (customer_portal_access)
);

-- User Permissions table (for granular permissions)
CREATE TABLE IF NOT EXISTS user_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    permission_value BOOLEAN DEFAULT TRUE,
    granted_by INT DEFAULT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES admin_users(id) ON DELETE SET NULL,

    UNIQUE KEY unique_user_permission (user_id, permission_name),
    INDEX idx_user_permissions (user_id),
    INDEX idx_permission_name (permission_name)
);

-- User Sessions table (for tracking active sessions)
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    ip_address VARCHAR(45) DEFAULT '',
    user_agent TEXT DEFAULT '',
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_sessions (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_active_sessions (is_active, expires_at),
    INDEX idx_last_activity (last_activity)
);

-- Insert sample users
INSERT INTO users (
    username, email, password_hash, first_name, last_name, user_type, status,
    customer_portal_access, email_verified, phone, company_name, job_title
) VALUES
-- Customer Portal Users
('john.doe', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', 'customer', 'active', TRUE, TRUE, '******-0101', 'ABC Corp', 'Purchasing Manager'),
('jane.smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', 'customer', 'active', TRUE, TRUE, '******-0102', 'XYZ Ltd', 'Finance Director'),

-- Staff Members
('mike.wilson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mike', 'Wilson', 'staff', 'active', FALSE, TRUE, '******-0201', 'Som Milk', 'Sales Representative'),
('sarah.johnson', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', 'staff', 'active', FALSE, TRUE, '******-0202', 'Som Milk', 'Accountant'),

-- Contractors
('david.brown', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'David', 'Brown', 'contractor', 'active', FALSE, TRUE, '******-0301', 'Brown Consulting', 'IT Consultant'),

-- Vendors
('lisa.garcia', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Lisa', 'Garcia', 'vendor', 'active', FALSE, TRUE, '******-0401', 'Garcia Supplies', 'Account Manager')

ON DUPLICATE KEY UPDATE username = username;

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) DEFAULT '',
    phone VARCHAR(20) DEFAULT '',
    address VARCHAR(255) DEFAULT '',
    city VARCHAR(100) DEFAULT '',
    state VARCHAR(50) DEFAULT '',
    zip_code VARCHAR(20) DEFAULT '',
    company VARCHAR(255) DEFAULT '',
    notes TEXT DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer_name (customer_name),
    INDEX idx_customer_email (email)
);

-- Invoices table
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT DEFAULT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) DEFAULT '',
    customer_phone VARCHAR(20) DEFAULT '',
    customer_address VARCHAR(255) DEFAULT '',
    customer_city VARCHAR(100) DEFAULT '',
    customer_state VARCHAR(50) DEFAULT '',
    customer_zip VARCHAR(20) DEFAULT '',
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0000,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    notes TEXT DEFAULT '',
    terms TEXT DEFAULT '',
    payment_method VARCHAR(50) DEFAULT '',
    payment_date DATE DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_name (customer_name),
    INDEX idx_status (status),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_due_date (due_date)
);

-- Invoice items table
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
);

-- Products/Services table (optional for future use)
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT DEFAULT '',
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    category VARCHAR(100) DEFAULT '',
    sku VARCHAR(100) DEFAULT '',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_product_name (name),
    INDEX idx_sku (sku),
    INDEX idx_category (category)
);

-- Payment records table (optional for future use)
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) DEFAULT '',
    reference_number VARCHAR(100) DEFAULT '',
    notes TEXT DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_payment_date (payment_date)
);

-- Triggers for automatic calculations
DELIMITER //

-- Trigger to update invoice totals when items are inserted
CREATE TRIGGER update_invoice_totals_insert
AFTER INSERT ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = NEW.invoice_id
    ),
    tax_amount = subtotal * tax_rate,
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = NEW.invoice_id;
END//

-- Trigger to update invoice totals when items are updated
CREATE TRIGGER update_invoice_totals_update
AFTER UPDATE ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = NEW.invoice_id
    ),
    tax_amount = subtotal * tax_rate,
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = NEW.invoice_id;
END//

-- Trigger to update invoice totals when items are deleted
CREATE TRIGGER update_invoice_totals_delete
AFTER DELETE ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = OLD.invoice_id
    ),
    tax_amount = subtotal * tax_rate,
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = OLD.invoice_id;
END//

DELIMITER ;

-- Insert default company information
INSERT INTO company_info (
    company_name, address, city, state, zip_code, phone, email
) VALUES (
    'Som Milk', '123 Dairy Street', 'Milk City', 'MC', '12345', 
    '+****************', '<EMAIL>'
) ON DUPLICATE KEY UPDATE id=id;

-- Insert sample customers
INSERT INTO customers (customer_name, email, phone, address, city, state, zip_code, company) VALUES
('John Smith', '<EMAIL>', '+****************', '123 Main St', 'Anytown', 'ST', '12345', 'Smith Corp'),
('Jane Doe', '<EMAIL>', '+****************', '456 Oak Ave', 'Somewhere', 'ST', '67890', 'Doe Industries'),
('Bob Johnson', '<EMAIL>', '+****************', '789 Pine Rd', 'Elsewhere', 'ST', '11111', 'Johnson LLC')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample invoices
INSERT INTO invoices (
    invoice_number, customer_name, customer_email, customer_address, 
    customer_city, customer_state, customer_zip, invoice_date, due_date,
    subtotal, tax_rate, tax_amount, total_amount, status, notes
) VALUES
('SM-1001', 'John Smith', '<EMAIL>', '123 Main St', 'Anytown', 'ST', '12345', 
 CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 1000.00, 0.0000, 0.00, 1000.00, 'pending', 'Sample invoice 1'),
('SM-1002', 'Jane Doe', '<EMAIL>', '456 Oak Ave', 'Somewhere', 'ST', '67890', 
 CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 1500.00, 0.0000, 0.00, 1500.00, 'paid', 'Sample invoice 2')
ON DUPLICATE KEY UPDATE id=id;

-- Insert sample invoice items
INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price) VALUES
(1, 'Premium Milk - 1 Gallon', 10.00, 50.00, 500.00),
(1, 'Organic Cheese - 1 lb', 20.00, 25.00, 500.00),
(2, 'Fresh Butter - 1 lb', 15.00, 40.00, 600.00),
(2, 'Yogurt - 32 oz', 30.00, 30.00, 900.00)
ON DUPLICATE KEY UPDATE id=id;

-- Invoice Status Log Table
CREATE TABLE invoice_status_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    old_status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') NOT NULL,
    new_status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_invoices_created_at ON invoices(created_at);
CREATE INDEX idx_invoices_updated_at ON invoices(updated_at);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_due_date ON invoices(due_date);
CREATE INDEX idx_customers_created_at ON customers(created_at);
CREATE INDEX idx_invoice_items_created_at ON invoice_items(created_at);
CREATE INDEX idx_invoice_status_log_invoice_id ON invoice_status_log(invoice_id);
CREATE INDEX idx_invoice_status_log_created_at ON invoice_status_log(created_at);
