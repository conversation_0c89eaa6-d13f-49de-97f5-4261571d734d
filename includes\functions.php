<?php
// Dashboard Functions

/**
 * Get total number of invoices
 */
function getTotalInvoices() {
    global $conn;
    $result = $conn->query("SELECT COUNT(*) as total FROM invoices");
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['total'] ?? 0;
    }
    return 0;
}

/**
 * Get total revenue from all invoices
 */
function getTotalRevenue() {
    global $conn;
    $result = $conn->query("SELECT SUM(total_amount) as total FROM invoices WHERE status != 'cancelled'");
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['total'] ?? 0;
    }
    return 0;
}

/**
 * Get monthly invoices count
 */
function getMonthlyInvoices() {
    global $conn;
    $result = $conn->query("SELECT COUNT(*) as total FROM invoices WHERE MONTH(invoice_date) = MONTH(CURDATE()) AND YEAR(invoice_date) = YEAR(CURDATE())");
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['total'] ?? 0;
    }
    return 0;
}

/**
 * Get pending invoices count
 */
function getPendingInvoices() {
    global $conn;
    $result = $conn->query("SELECT COUNT(*) as total FROM invoices WHERE status = 'pending'");
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['total'] ?? 0;
    }
    return 0;
}

/**
 * Get recent invoices
 */
function getRecentInvoices($limit = 5) {
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM invoices ORDER BY created_at DESC LIMIT ?");
    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Get pending invoices count
 */
function getPendingInvoicesCount() {
    global $conn;

    $result = $conn->query("SELECT COUNT(*) as count FROM invoices WHERE status = 'pending'");
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['count'] ?? 0;
    }
    return 0;
}

/**
 * Get overdue invoices count
 */
function getOverdueInvoicesCount() {
    global $conn;

    $result = $conn->query("SELECT COUNT(*) as count FROM invoices WHERE status = 'pending' AND due_date < CURDATE()");
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['count'] ?? 0;
    }
    return 0;
}

/**
 * Get monthly revenue for the last 6 months
 */
function getMonthlyRevenue() {
    global $conn;

    // Generate the last 6 months with zero revenue as default
    $months = [];
    for ($i = 5; $i >= 0; $i--) {
        $date = date('Y-m-01', strtotime("-$i months"));
        $months[date('Y-m', strtotime($date))] = [
            'month' => date('Y-m', strtotime($date)),
            'month_name' => date('F Y', strtotime($date)),
            'revenue' => 0
        ];
    }

    // Get actual revenue data
    $result = $conn->query("
        SELECT
            DATE_FORMAT(invoice_date, '%Y-%m') as month,
            DATE_FORMAT(invoice_date, '%M %Y') as month_name,
            SUM(total_amount) as revenue
        FROM invoices
        WHERE status != 'cancelled'
        AND invoice_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(invoice_date, '%Y-%m')
        ORDER BY month ASC
    ");

    // Merge actual data with default months
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            if (isset($months[$row['month']])) {
                $months[$row['month']]['revenue'] = floatval($row['revenue']);
            }
        }
    }

    // Return as indexed array
    return array_values($months);
}

/**
 * Get invoice status statistics
 */
function getInvoiceStatusStats() {
    global $conn;

    // Define all possible statuses with defaults
    $defaultStats = [
        'draft' => ['status' => 'draft', 'count' => 0, 'total_amount' => 0],
        'pending' => ['status' => 'pending', 'count' => 0, 'total_amount' => 0],
        'paid' => ['status' => 'paid', 'count' => 0, 'total_amount' => 0],
        'overdue' => ['status' => 'overdue', 'count' => 0, 'total_amount' => 0],
        'cancelled' => ['status' => 'cancelled', 'count' => 0, 'total_amount' => 0]
    ];

    $result = $conn->query("
        SELECT
            status,
            COUNT(*) as count,
            SUM(total_amount) as total_amount
        FROM invoices
        GROUP BY status
        ORDER BY count DESC
    ");

    // Merge actual data with defaults
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            if (isset($defaultStats[$row['status']])) {
                $defaultStats[$row['status']]['count'] = intval($row['count']);
                $defaultStats[$row['status']]['total_amount'] = floatval($row['total_amount']);
            }
        }
    }

    // Return only statuses that have data (count > 0) or return at least one default
    $stats = array_filter($defaultStats, function($stat) {
        return $stat['count'] > 0;
    });

    // If no data at all, return a default "No Data" entry
    if (empty($stats)) {
        return [['status' => 'no_data', 'count' => 1, 'total_amount' => 0]];
    }

    return array_values($stats);
}

/**
 * Update invoice status with workflow validation
 */
function updateInvoiceStatus($invoiceId, $newStatus, $notes = '') {
    global $conn;

    // Valid status transitions
    $validTransitions = [
        'draft' => ['pending', 'cancelled'],
        'pending' => ['paid', 'overdue', 'cancelled'],
        'overdue' => ['paid', 'cancelled'],
        'paid' => [], // Final state
        'cancelled' => ['draft'] // Can reopen cancelled invoices
    ];

    // Get current status
    $stmt = $conn->prepare("SELECT status FROM invoices WHERE id = ?");
    $stmt->bind_param("i", $invoiceId);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();

    if (!$invoice) {
        return ['success' => false, 'message' => 'Invoice not found'];
    }

    $currentStatus = $invoice['status'];

    // Check if transition is valid
    if (!in_array($newStatus, $validTransitions[$currentStatus])) {
        return ['success' => false, 'message' => "Cannot change status from {$currentStatus} to {$newStatus}"];
    }

    try {
        $conn->autocommit(FALSE);

        // Update invoice status
        $updateStmt = $conn->prepare("UPDATE invoices SET status = ?, updated_at = NOW() WHERE id = ?");
        $updateStmt->bind_param("si", $newStatus, $invoiceId);
        $updateStmt->execute();

        // Log status change
        $logStmt = $conn->prepare("
            INSERT INTO invoice_status_log (invoice_id, old_status, new_status, notes, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");
        $logStmt->bind_param("isss", $invoiceId, $currentStatus, $newStatus, $notes);
        $logStmt->execute();

        // Update overdue status automatically
        if ($newStatus === 'pending') {
            updateOverdueInvoices();
        }

        $conn->commit();
        return ['success' => true, 'message' => 'Status updated successfully'];

    } catch (mysqli_sql_exception $e) {
        $conn->rollback();
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    } finally {
        $conn->autocommit(TRUE);
    }
}

/**
 * Automatically update overdue invoices
 */
function updateOverdueInvoices() {
    global $conn;

    $conn->query("
        UPDATE invoices
        SET status = 'overdue'
        WHERE status = 'pending'
        AND due_date < CURDATE()
    ");
}

/**
 * Get invoice status history
 */
function getInvoiceStatusHistory($invoiceId) {
    global $conn;

    $stmt = $conn->prepare("
        SELECT old_status, new_status, notes, created_at
        FROM invoice_status_log
        WHERE invoice_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->bind_param("i", $invoiceId);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Get company information
 */
function getCompanyInfo() {
    global $conn;

    // Get settings from company_settings table
    $result = $conn->query("SELECT setting_key, setting_value FROM company_settings");
    $settings = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Return company info with defaults if settings don't exist
    return [
        'company_name' => $settings['company_name'] ?? COMPANY_NAME,
        'address' => $settings['company_address'] ?? COMPANY_ADDRESS,
        'city' => $settings['company_city'] ?? COMPANY_CITY,
        'state' => $settings['company_state'] ?? COMPANY_STATE,
        'zip_code' => $settings['company_zip'] ?? COMPANY_ZIP,
        'phone' => $settings['company_phone'] ?? COMPANY_PHONE,
        'email' => $settings['company_email'] ?? COMPANY_EMAIL,
        'bank_name' => $settings['bank_name'] ?? '',
        'account_number' => $settings['account_number'] ?? '',
        'routing_number' => $settings['routing_number'] ?? ''
    ];
}

/**
 * Create a new invoice
 */
function createInvoice($data) {
    global $conn;
    
    $conn->autocommit(FALSE);
    
    try {
        // Insert main invoice record
        $stmt = $conn->prepare("
            INSERT INTO invoices (
                invoice_number, customer_name, customer_email, customer_phone, customer_address,
                invoice_date, due_date, subtotal, tax_amount, total_amount, status, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");

        // Prepare variables for bind_param (must be passed by reference)
        $invoice_number = $data['invoice_number'];
        $customer_name = $data['customer_name'];
        $customer_email = $data['customer_email'] ?? '';
        $customer_phone = $data['customer_phone'] ?? '';
        $customer_address = $data['customer_address'] ?? '';
        $invoice_date = $data['invoice_date'];
        $due_date = $data['due_date'] ?? $data['invoice_date'];
        $subtotal = $data['subtotal'];
        $tax_amount = $data['tax_amount'];
        $total_amount = $data['total_amount'];
        $status = $data['status'] ?? 'pending';
        $notes = $data['notes'] ?? '';

        $stmt->bind_param("sssssssdddss",
            $invoice_number,
            $customer_name,
            $customer_email,
            $customer_phone,
            $customer_address,
            $invoice_date,
            $due_date,
            $subtotal,
            $tax_amount,
            $total_amount,
            $status,
            $notes
        );
        
        $stmt->execute();
        $invoiceId = $conn->insert_id;
        
        // Insert invoice items
        if (!empty($data['items'])) {
            $itemStmt = $conn->prepare("
                INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($data['items'] as $item) {
                // Prepare variables for bind_param (must be passed by reference)
                $description = $item['description'];
                $quantity = $item['quantity'];
                $unit_price = $item['unit_price'];
                $total_price = $item['total_price'];

                $itemStmt->bind_param("isddd",
                    $invoiceId,
                    $description,
                    $quantity,
                    $unit_price,
                    $total_price
                );
                $itemStmt->execute();
            }
        }
        
        $conn->commit();
        return ['success' => true, 'invoice_id' => $invoiceId];

    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error creating invoice: " . $e->getMessage());
        return ['success' => false, 'message' => $e->getMessage()];
    } finally {
        $conn->autocommit(TRUE);
    }
}

/**
 * Get invoice by ID
 */
function getInvoiceById($id) {
    global $conn;
    
    $stmt = $conn->prepare("SELECT * FROM invoices WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    
    if ($invoice) {
        // Get invoice items
        $itemStmt = $conn->prepare("SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY id");
        $itemStmt->bind_param("i", $id);
        $itemStmt->execute();
        $itemResult = $itemStmt->get_result();
        $invoice['items'] = $itemResult->fetch_all(MYSQLI_ASSOC);
    }
    
    return $invoice;
}

/**
 * Get all invoices with pagination and search
 */
function getInvoices($page = 1, $limit = 10, $search = '') {
    global $conn;
    
    $offset = ($page - 1) * $limit;
    
    $whereClause = '';
    if (!empty($search)) {
        $whereClause = "WHERE invoice_number LIKE ? OR customer_name LIKE ?";
        $searchParam = "%$search%";
    }
    
    $sql = "SELECT * FROM invoices $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $stmt = $conn->prepare($sql);
    
    if (!empty($search)) {
        $stmt->bind_param("ssii", $searchParam, $searchParam, $limit, $offset);
    } else {
        $stmt->bind_param("ii", $limit, $offset);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Get total invoice count for pagination
 */
function getTotalInvoiceCount($search = '') {
    global $conn;
    
    $whereClause = '';
    if (!empty($search)) {
        $whereClause = "WHERE invoice_number LIKE ? OR customer_name LIKE ?";
        $searchParam = "%$search%";
    }
    
    $sql = "SELECT COUNT(*) as total FROM invoices $whereClause";
    
    if (!empty($search)) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $searchParam, $searchParam);
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $conn->query($sql);
    }
    
    if ($result) {
        $row = $result->fetch_assoc();
        return $row['total'] ?? 0;
    }
    return 0;
}

/**
 * Get invoices with advanced filters
 */
function getInvoicesWithFilters($page = 1, $limit = 10, $filters = []) {
    global $conn;

    $offset = ($page - 1) * $limit;

    $sql = "SELECT * FROM invoices";

    $whereConditions = [];
    $params = [];
    $types = "";

    // Search filter
    if (!empty($filters['search'])) {
        $whereConditions[] = "(invoice_number LIKE ? OR customer_name LIKE ? OR customer_email LIKE ?)";
        $searchTerm = "%" . $filters['search'] . "%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        $types .= "sss";
    }

    // Status filter
    if (!empty($filters['status'])) {
        $whereConditions[] = "status = ?";
        $params[] = $filters['status'];
        $types .= "s";
    }

    // Date range filter
    if (!empty($filters['date_from'])) {
        $whereConditions[] = "invoice_date >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }

    if (!empty($filters['date_to'])) {
        $whereConditions[] = "invoice_date <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }

    // Amount range filter
    if ($filters['amount_min'] !== null && $filters['amount_min'] > 0) {
        $whereConditions[] = "total_amount >= ?";
        $params[] = $filters['amount_min'];
        $types .= "d";
    }

    if ($filters['amount_max'] !== null && $filters['amount_max'] > 0) {
        $whereConditions[] = "total_amount <= ?";
        $params[] = $filters['amount_max'];
        $types .= "d";
    }

    // Add WHERE clause if conditions exist
    if (!empty($whereConditions)) {
        $sql .= " WHERE " . implode(" AND ", $whereConditions);
    }

    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";

    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->fetch_all(MYSQLI_ASSOC);
}

/**
 * Get total invoice count with filters
 */
function getTotalInvoiceCountWithFilters($filters = []) {
    global $conn;

    $sql = "SELECT COUNT(*) as total FROM invoices";

    $whereConditions = [];
    $params = [];
    $types = "";

    // Search filter
    if (!empty($filters['search'])) {
        $whereConditions[] = "(invoice_number LIKE ? OR customer_name LIKE ? OR customer_email LIKE ?)";
        $searchTerm = "%" . $filters['search'] . "%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        $types .= "sss";
    }

    // Status filter
    if (!empty($filters['status'])) {
        $whereConditions[] = "status = ?";
        $params[] = $filters['status'];
        $types .= "s";
    }

    // Date range filter
    if (!empty($filters['date_from'])) {
        $whereConditions[] = "invoice_date >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }

    if (!empty($filters['date_to'])) {
        $whereConditions[] = "invoice_date <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }

    // Amount range filter
    if ($filters['amount_min'] !== null && $filters['amount_min'] > 0) {
        $whereConditions[] = "total_amount >= ?";
        $params[] = $filters['amount_min'];
        $types .= "d";
    }

    if ($filters['amount_max'] !== null && $filters['amount_max'] > 0) {
        $whereConditions[] = "total_amount <= ?";
        $params[] = $filters['amount_max'];
        $types .= "d";
    }

    // Add WHERE clause if conditions exist
    if (!empty($whereConditions)) {
        $sql .= " WHERE " . implode(" AND ", $whereConditions);
    }

    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    return $row['total'];
}

/**
 * Create database backup
 */
function createDatabaseBackup() {
    global $conn;

    // Create backups directory if it doesn't exist
    $backup_dir = 'backups';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }

    // Generate backup filename
    $timestamp = date('Y-m-d_H-i-s');
    $filename = "sommilk_backup_{$timestamp}.sql";
    $filepath = $backup_dir . '/' . $filename;

    try {
        // Get database name from config
        $db_name = DB_NAME;

        // Create backup content
        $backup_content = "-- Som Milk Invoice System Database Backup\n";
        $backup_content .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $backup_content .= "-- Database: {$db_name}\n\n";

        // Get all tables
        $tables_result = $conn->query("SHOW TABLES");
        $tables = [];
        while ($row = $tables_result->fetch_array()) {
            $tables[] = $row[0];
        }

        // Backup each table
        foreach ($tables as $table) {
            // Get table structure
            $create_table_result = $conn->query("SHOW CREATE TABLE `{$table}`");
            $create_table_row = $create_table_result->fetch_array();

            $backup_content .= "\n-- Table structure for table `{$table}`\n";
            $backup_content .= "DROP TABLE IF EXISTS `{$table}`;\n";
            $backup_content .= $create_table_row[1] . ";\n\n";

            // Get table data
            $data_result = $conn->query("SELECT * FROM `{$table}`");
            if ($data_result->num_rows > 0) {
                $backup_content .= "-- Dumping data for table `{$table}`\n";

                while ($row = $data_result->fetch_assoc()) {
                    $columns = array_keys($row);
                    $values = array_values($row);

                    // Escape values
                    $escaped_values = array_map(function($value) use ($conn) {
                        return $value === null ? 'NULL' : "'" . $conn->real_escape_string($value) . "'";
                    }, $values);

                    $backup_content .= "INSERT INTO `{$table}` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $escaped_values) . ");\n";
                }
                $backup_content .= "\n";
            }
        }

        // Write backup to file
        if (file_put_contents($filepath, $backup_content)) {
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to write backup file'
            ];
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Get backup files
 */
function getBackupFiles() {
    $backup_dir = 'backups';
    $files = [];

    if (is_dir($backup_dir)) {
        $scan = scandir($backup_dir);
        foreach ($scan as $file) {
            if ($file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) == 'sql') {
                $filepath = $backup_dir . '/' . $file;
                $files[] = [
                    'name' => $file,
                    'size' => filesize($filepath),
                    'modified' => filemtime($filepath)
                ];
            }
        }

        // Sort by modification time (newest first)
        usort($files, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
    }

    return $files;
}

/**
 * Format file size
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);

    $bytes /= pow(1024, $pow);

    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * Delete invoice
 */
function deleteInvoice($id) {
    global $conn;
    
    $conn->autocommit(FALSE);
    
    try {
        // Delete invoice items first
        $stmt = $conn->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        
        // Delete invoice
        $stmt = $conn->prepare("DELETE FROM invoices WHERE id = ?");
        $stmt->bind_param("i", $id);
        $stmt->execute();
        
        $conn->commit();
        return true;
        
    } catch (Exception $e) {
        $conn->rollback();
        error_log("Error deleting invoice: " . $e->getMessage());
        return false;
    } finally {
        $conn->autocommit(TRUE);
    }
}

/**
 * Generate next invoice number
 */
function generateInvoiceNumber() {
    global $conn;
    
    $result = $conn->query("SELECT MAX(CAST(SUBSTRING(invoice_number, 4) AS UNSIGNED)) as max_num FROM invoices WHERE invoice_number LIKE 'SM-%'");
    if ($result) {
        $row = $result->fetch_assoc();
        $nextNum = ($row['max_num'] ?? 1000) + 1;
        return INVOICE_PREFIX . $nextNum;
    }
    return INVOICE_PREFIX . '1001';
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    global $conn;
    return $conn->real_escape_string(trim($data));
}

/**
 * Format currency
 */
function formatCurrency($amount) {
    return '$' . number_format($amount, 2);
}

/**
 * Format date
 */
function formatDate($date, $format = 'M j, Y') {
    return date($format, strtotime($date));
}
?>
