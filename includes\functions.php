<?php
// Dashboard Functions



/**
 * Get total number of invoices
 */
function getTotalInvoices() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM invoices");
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total invoices: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get total revenue from all invoices
 */
function getTotalRevenue() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT SUM(total_amount) as total FROM invoices WHERE status != 'cancelled'");
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting total revenue: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get monthly invoices count
 */
function getMonthlyInvoices() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM invoices WHERE MONTH(invoice_date) = MONTH(CURDATE()) AND YEAR(invoice_date) = YEAR(CURDATE())");
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting monthly invoices: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get pending invoices count
 */
function getPendingInvoices() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM invoices WHERE status = 'pending'");
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting pending invoices: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get recent invoices
 */
function getRecentInvoices($limit = 5) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT * FROM invoices ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        error_log("Error getting recent invoices: " . $e->getMessage());
        return [];
    }
}

/**
 * Get company information
 */
function getCompanyInfo() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT * FROM company_info LIMIT 1");
        $result = $stmt->fetch();
        
        if ($result) {
            return $result;
        } else {
            // Return default company info if not found in database
            return [
                'company_name' => COMPANY_NAME,
                'address' => COMPANY_ADDRESS,
                'city' => COMPANY_CITY,
                'state' => COMPANY_STATE,
                'zip_code' => COMPANY_ZIP,
                'phone' => COMPANY_PHONE,
                'email' => COMPANY_EMAIL,
                'logo_path' => 'assets/images/logo.png',
                'bank_name' => 'First National Bank',
                'account_number' => '**********',
                'routing_number' => '*********'
            ];
        }
    } catch (PDOException $e) {
        error_log("Error getting company info: " . $e->getMessage());
        return [];
    }
}

/**
 * Create a new invoice
 */
function createInvoice($data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Insert main invoice record
        $stmt = $pdo->prepare("
            INSERT INTO invoices (
                invoice_number, invoice_date, due_date, customer_name, 
                customer_email, customer_phone, customer_address, 
                subtotal, tax_amount, total_amount, status, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['invoice_number'],
            $data['invoice_date'],
            $data['due_date'],
            $data['customer_name'],
            $data['customer_email'],
            $data['customer_phone'],
            $data['customer_address'],
            $data['subtotal'],
            $data['tax_amount'],
            $data['total_amount'],
            $data['status'] ?? 'pending',
            $data['notes'] ?? ''
        ]);
        
        $invoiceId = $pdo->lastInsertId();
        
        // Insert invoice items
        if (!empty($data['items'])) {
            $itemStmt = $pdo->prepare("
                INSERT INTO invoice_items (
                    invoice_id, description, quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($data['items'] as $item) {
                $itemStmt->execute([
                    $invoiceId,
                    $item['description'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price']
                ]);
            }
        }
        
        $pdo->commit();
        return $invoiceId;
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Error creating invoice: " . $e->getMessage());
        return false;
    }
}

/**
 * Get invoice by ID
 */
function getInvoiceById($id) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$id]);
        $invoice = $stmt->fetch();
        
        if ($invoice) {
            // Get invoice items
            $itemStmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
            $itemStmt->execute([$id]);
            $invoice['items'] = $itemStmt->fetchAll();
        }
        
        return $invoice;
    } catch (PDOException $e) {
        error_log("Error getting invoice: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all invoices with pagination and search
 */
function getInvoices($page = 1, $limit = 10, $search = '') {
    global $pdo;
    try {
        $offset = ($page - 1) * $limit;
        
        $whereClause = '';
        $params = [];
        
        if (!empty($search)) {
            $whereClause = "WHERE invoice_number LIKE ? OR customer_name LIKE ?";
            $params = ["%$search%", "%$search%"];
        }
        
        $stmt = $pdo->prepare("
            SELECT * FROM invoices 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt->execute($params);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("Error getting invoices: " . $e->getMessage());
        return [];
    }
}

/**
 * Get total invoice count for pagination
 */
function getTotalInvoiceCount($search = '') {
    global $pdo;
    try {
        $whereClause = '';
        $params = [];
        
        if (!empty($search)) {
            $whereClause = "WHERE invoice_number LIKE ? OR customer_name LIKE ?";
            $params = ["%$search%", "%$search%"];
        }
        
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM invoices $whereClause");
        $stmt->execute($params);
        $result = $stmt->fetch();
        
        return $result['total'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error getting invoice count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Update invoice status
 */
function updateInvoiceStatus($id, $status) {
    global $pdo;
    try {
        $stmt = $pdo->prepare("UPDATE invoices SET status = ?, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$status, $id]);
    } catch (PDOException $e) {
        error_log("Error updating invoice status: " . $e->getMessage());
        return false;
    }
}

/**
 * Delete invoice
 */
function deleteInvoice($id) {
    global $pdo;
    try {
        $pdo->beginTransaction();
        
        // Delete invoice items first
        $stmt = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
        $stmt->execute([$id]);
        
        // Delete invoice
        $stmt = $pdo->prepare("DELETE FROM invoices WHERE id = ?");
        $stmt->execute([$id]);
        
        $pdo->commit();
        return true;
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Error deleting invoice: " . $e->getMessage());
        return false;
    }
}
?>
