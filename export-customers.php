<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if export is requested
if (!isset($_GET['export']) || $_GET['export'] !== 'csv') {
    header("Location: backup-export.php");
    exit;
}

// Get all customers
$stmt = $conn->prepare("SELECT * FROM customers ORDER BY created_at DESC");
$stmt->execute();
$result = $stmt->get_result();
$customers = $result->fetch_all(MYSQLI_ASSOC);

// Set headers for CSV download
$filename = 'customers_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add CSV headers
$headers = [
    'ID',
    'Name',
    'Email',
    'Phone',
    'Address',
    'Created At',
    'Updated At'
];

fputcsv($output, $headers);

// Add customer data
foreach ($customers as $customer) {
    $row = [
        $customer['id'],
        $customer['name'],
        $customer['email'],
        $customer['phone'] ?: '',
        $customer['address'] ?: '',
        $customer['created_at'],
        $customer['updated_at']
    ];
    
    fputcsv($output, $row);
}

// Close the file pointer
fclose($output);
exit;
?>
