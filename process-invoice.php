<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: create-invoice.php');
    exit;
}

$errors = [];
$success = false;

try {
    // Validate required fields
    if (empty($_POST['customer_name'])) {
        $errors[] = "Customer name is required.";
    }
    
    if (empty($_POST['invoice_date'])) {
        $errors[] = "Invoice date is required.";
    }
    
    if (empty($_POST['items']) || !is_array($_POST['items'])) {
        $errors[] = "At least one invoice item is required.";
    }
    
    // Validate items
    $valid_items = [];
    if (!empty($_POST['items'])) {
        foreach ($_POST['items'] as $index => $item) {
            if (empty($item['description']) || empty($item['quantity']) || empty($item['unit_price'])) {
                continue; // Skip empty items
            }
            
            $quantity = floatval($item['quantity']);
            $unit_price = floatval($item['unit_price']);
            
            if ($quantity <= 0 || $unit_price <= 0) {
                $errors[] = "Item " . ($index + 1) . " has invalid quantity or price.";
                continue;
            }
            
            $valid_items[] = [
                'description' => sanitizeInput($item['description']),
                'quantity' => $quantity,
                'unit_price' => $unit_price,
                'total_price' => $quantity * $unit_price
            ];
        }
    }
    
    if (empty($valid_items)) {
        $errors[] = "At least one valid invoice item is required.";
    }
    
    // If no errors, create the invoice
    if (empty($errors)) {
        $conn->autocommit(FALSE);

        // Calculate totals
        $subtotal = array_sum(array_column($valid_items, 'total_price'));
        $tax_rate = floatval($_POST['tax_rate'] ?? 0);
        $tax_amount = $subtotal * ($tax_rate / 100);
        $total_amount = $subtotal + $tax_amount;

        // Generate invoice number
        $invoice_number = generateInvoiceNumber();

        // Create or get customer
        $customer_id = null;
        if (!empty($_POST['customer_name'])) {
            // Check if customer exists
            $stmt = $conn->prepare("SELECT id FROM customers WHERE name = ? AND email = ?");
            $customer_name = sanitizeInput($_POST['customer_name']);
            $customer_email = sanitizeInput($_POST['customer_email'] ?? '');
            $stmt->bind_param("ss", $customer_name, $customer_email);
            $stmt->execute();
            $result = $stmt->get_result();
            $existing_customer = $result->fetch_assoc();

            if ($existing_customer) {
                $customer_id = $existing_customer['id'];
            } else {
                // Create new customer
                $stmt = $conn->prepare("
                    INSERT INTO customers (name, email, phone, address, created_at)
                    VALUES (?, ?, ?, ?, NOW())
                ");
                $customer_phone = sanitizeInput($_POST['customer_phone'] ?? '');
                $customer_address = sanitizeInput($_POST['customer_address'] ?? '');
                $stmt->bind_param("ssss", $customer_name, $customer_email, $customer_phone, $customer_address);
                $stmt->execute();
                $customer_id = $conn->insert_id;
            }
        }
        
        // Create invoice
        $stmt = $conn->prepare("
            INSERT INTO invoices (
                invoice_number, customer_id, customer_name, customer_email,
                customer_phone, customer_address, invoice_date, due_date,
                subtotal, tax_rate, tax_amount, total_amount, status, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");

        $invoice_customer_name = sanitizeInput($_POST['customer_name']);
        $invoice_customer_email = sanitizeInput($_POST['customer_email'] ?? '');
        $invoice_customer_phone = sanitizeInput($_POST['customer_phone'] ?? '');
        $invoice_customer_address = sanitizeInput($_POST['customer_address'] ?? '');
        $invoice_date = sanitizeInput($_POST['invoice_date']);
        $due_date = !empty($_POST['due_date']) ? sanitizeInput($_POST['due_date']) : null;
        $status = sanitizeInput($_POST['status'] ?? 'draft');
        $notes = sanitizeInput($_POST['notes'] ?? '');

        $stmt->bind_param("sissssssddddss",
            $invoice_number,
            $customer_id,
            $invoice_customer_name,
            $invoice_customer_email,
            $invoice_customer_phone,
            $invoice_customer_address,
            $invoice_date,
            $due_date,
            $subtotal,
            $tax_rate,
            $tax_amount,
            $total_amount,
            $status,
            $notes
        );
        $stmt->execute();

        $invoice_id = $conn->insert_id;
        
        // Create invoice items
        $stmt = $conn->prepare("
            INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, total_price)
            VALUES (?, ?, ?, ?, ?)
        ");

        foreach ($valid_items as $item) {
            $stmt->bind_param("isddd",
                $invoice_id,
                $item['description'],
                $item['quantity'],
                $item['unit_price'],
                $item['total_price']
            );
            $stmt->execute();
        }

        $conn->commit();
        
        $_SESSION['success_message'] = "Invoice {$invoice_number} created successfully!";
        
        // Redirect based on action
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'save_and_view':
                    header("Location: view-invoice.php?id={$invoice_id}");
                    break;
                case 'save_and_pdf':
                    header("Location: generate-pdf.php?id={$invoice_id}");
                    break;
                case 'save_and_new':
                    header("Location: create-invoice.php");
                    break;
                default:
                    header("Location: invoices.php");
            }
        } else {
            header("Location: invoices.php");
        }
        exit;
    }
    
} catch (mysqli_sql_exception $e) {
    $conn->rollback();
    error_log("Database error in process-invoice.php: " . $e->getMessage());
    $errors[] = "Database error occurred. Please try again.";
} catch (Exception $e) {
    $conn->rollback();
    error_log("General error in process-invoice.php: " . $e->getMessage());
    $errors[] = "An error occurred while processing the invoice. Please try again.";
}

// If we get here, there were errors
$_SESSION['form_errors'] = $errors;
$_SESSION['form_data'] = $_POST;

header('Location: create-invoice.php');
exit;
?>
