-- Som Milk Invoice Management System Database Schema
-- Created: 2025-07-07
-- Version: 1.0.0

-- Create database
CREATE DATABASE IF NOT EXISTS sommilk_invoice CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE sommilk_invoice;

-- Company Information Table (Static Data)
CREATE TABLE company_info (
    id INT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(255) NOT NULL DEFAULT 'Som Milk',
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    logo_path VARCHAR(500),
    
    -- Banking Information
    bank_name VARCHAR(255),
    account_number VARCHAR(100),
    routing_number VARCHAR(50),
    account_holder_name VA<PERSON>HA<PERSON>(255),
    
    -- Additional Settings
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    currency VARCHAR(10) DEFAULT 'USD',
    invoice_prefix VARCHAR(20) DEFAULT 'SM-',
    invoice_terms TEXT,
    invoice_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Customers Table
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    
    -- Additional Information
    tax_id VARCHAR(50),
    payment_terms VARCHAR(100),
    notes TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes
    INDEX idx_customer_name (customer_name),
    INDEX idx_email (email),
    INDEX idx_status (status)
);

-- Invoices Table (Main Invoice Data)
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INT NULL, -- Can be NULL for one-time customers
    
    -- Invoice Dates
    invoice_date DATE NOT NULL,
    due_date DATE,
    
    -- Customer Information (stored directly for flexibility)
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(50),
    customer_address TEXT,
    
    -- Financial Information
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    
    -- Status and Payment
    status ENUM('draft', 'pending', 'paid', 'overdue', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid',
    payment_date DATE NULL,
    payment_method VARCHAR(100),
    
    -- Additional Information
    notes TEXT,
    terms TEXT,
    internal_notes TEXT,
    
    -- File Information
    pdf_path VARCHAR(500),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_name (customer_name),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_total_amount (total_amount),
    INDEX idx_created_at (created_at)
);

-- Invoice Items Table (Line Items)
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    
    -- Item Information
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    
    -- Additional Item Details
    item_code VARCHAR(100),
    unit VARCHAR(50) DEFAULT 'pcs',
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    discount_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Sorting
    sort_order INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_sort_order (sort_order)
);

-- Payment Records Table (For tracking payments)
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    
    -- Payment Information
    payment_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(100),
    reference_number VARCHAR(255),
    
    -- Additional Information
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign Key
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_payment_date (payment_date)
);

-- System Settings Table
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Index
    INDEX idx_setting_key (setting_key)
);

-- Insert default company information
INSERT INTO company_info (
    company_name, address, city, state, zip_code, country,
    phone, email, website,
    bank_name, account_number, routing_number, account_holder_name,
    tax_rate, currency, invoice_prefix,
    invoice_terms, invoice_notes
) VALUES (
    'Som Milk',
    '123 Dairy Street',
    'Milk City',
    'MC',
    '12345',
    'United States',
    '+****************',
    '<EMAIL>',
    'www.sommilk.com',
    'First National Bank',
    '**********',
    '*********',
    'Som Milk LLC',
    0.00,
    'USD',
    'SM-',
    'Payment is due within 30 days of invoice date. Late payments may be subject to a 1.5% monthly service charge.',
    'Thank you for your business! We appreciate your partnership with Som Milk.'
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('app_name', 'Som Milk Invoice Management', 'string', 'Application name'),
('app_version', '1.0.0', 'string', 'Application version'),
('default_due_days', '30', 'number', 'Default number of days for invoice due date'),
('max_file_size', '5242880', 'number', 'Maximum file upload size in bytes (5MB)'),
('allowed_file_types', 'jpg,jpeg,png,pdf,doc,docx', 'string', 'Allowed file upload types'),
('invoice_auto_number', 'true', 'boolean', 'Enable automatic invoice numbering'),
('email_notifications', 'false', 'boolean', 'Enable email notifications'),
('backup_enabled', 'false', 'boolean', 'Enable automatic backups');

-- Create some sample customers for testing
INSERT INTO customers (customer_name, contact_person, email, phone, address, city, state, zip_code) VALUES
('Diplomatic Hotel', 'John Manager', '<EMAIL>', '+****************', '456 Hotel Avenue', 'Business City', 'BC', '54321'),
('Grand Restaurant', 'Sarah Chef', '<EMAIL>', '+****************', '789 Food Street', 'Culinary Town', 'CT', '67890'),
('City Cafe', 'Mike Owner', '<EMAIL>', '+****************', '321 Coffee Lane', 'Bean City', 'BC', '09876');

-- Create triggers to automatically update invoice totals
DELIMITER //

CREATE TRIGGER update_invoice_total_after_item_insert
AFTER INSERT ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = NEW.invoice_id
    ),
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = NEW.invoice_id;
END//

CREATE TRIGGER update_invoice_total_after_item_update
AFTER UPDATE ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = NEW.invoice_id
    ),
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = NEW.invoice_id;
END//

CREATE TRIGGER update_invoice_total_after_item_delete
AFTER DELETE ON invoice_items
FOR EACH ROW
BEGIN
    UPDATE invoices 
    SET subtotal = (
        SELECT COALESCE(SUM(total_price), 0) 
        FROM invoice_items 
        WHERE invoice_id = OLD.invoice_id
    ),
    total_amount = subtotal + tax_amount - discount_amount
    WHERE id = OLD.invoice_id;
END//

DELIMITER ;

-- Create views for common queries
CREATE VIEW invoice_summary AS
SELECT 
    i.id,
    i.invoice_number,
    i.invoice_date,
    i.due_date,
    i.customer_name,
    i.total_amount,
    i.status,
    i.payment_status,
    COUNT(ii.id) as item_count,
    DATEDIFF(CURDATE(), i.due_date) as days_overdue
FROM invoices i
LEFT JOIN invoice_items ii ON i.id = ii.invoice_id
GROUP BY i.id;

CREATE VIEW monthly_revenue AS
SELECT 
    YEAR(invoice_date) as year,
    MONTH(invoice_date) as month,
    MONTHNAME(invoice_date) as month_name,
    COUNT(*) as invoice_count,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as average_invoice
FROM invoices 
WHERE status != 'cancelled'
GROUP BY YEAR(invoice_date), MONTH(invoice_date)
ORDER BY year DESC, month DESC;
