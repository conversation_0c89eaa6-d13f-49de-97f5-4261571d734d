<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "File Management & Security Test\n";
echo "===============================\n\n";

// 1. Directory Structure Test
echo "1. Directory Structure Test\n";
echo "---------------------------\n";
$requiredDirs = [
    'uploads/',
    'uploads/invoices/',
    'uploads/backups/',
    'uploads/logos/',
    'includes/',
    'assets/',
    'assets/css/',
    'assets/js/'
];

foreach ($requiredDirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? "✅ Writable" : "❌ Not writable";
        echo "✅ $dir exists - $writable\n";
    } else {
        echo "❌ $dir missing\n";
    }
}

// 2. File Upload Security Test
echo "\n2. File Upload Security Test\n";
echo "----------------------------\n";

// Check if upload functions exist
$uploadFunctions = ['validateFileUpload', 'handleFileUpload', 'generateSecureFilename'];
foreach ($uploadFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ Upload function '$func' exists\n";
    } else {
        echo "⚠️  Upload function '$func' not found (may be inline)\n";
    }
}

// Check upload configuration
echo "Upload Configuration:\n";
echo "- Max file size: " . (MAX_FILE_SIZE / 1024 / 1024) . "MB\n";
echo "- Upload directory: " . UPLOAD_DIR . "\n";

// 3. Backup System Test
echo "\n3. Backup System Test\n";
echo "---------------------\n";

// Check if backup functions exist
if (function_exists('createDatabaseBackup')) {
    echo "✅ Database backup function exists\n";
} else {
    echo "⚠️  Database backup function not found\n";
}

// Test backup directory
$backupDir = 'uploads/backups/';
if (is_dir($backupDir) && is_writable($backupDir)) {
    echo "✅ Backup directory ready\n";
    
    // Create a test backup file
    $testBackup = $backupDir . 'test_backup_' . date('Y-m-d_H-i-s') . '.sql';
    if (file_put_contents($testBackup, "-- Test backup file\nSELECT 1;")) {
        echo "✅ Backup file creation test passed\n";
        unlink($testBackup); // Clean up
    } else {
        echo "❌ Backup file creation failed\n";
    }
} else {
    echo "❌ Backup directory not ready\n";
}

// 4. Export System Test
echo "\n4. Export System Test\n";
echo "---------------------\n";

// Test CSV export functionality
try {
    // Test invoice export
    $result = $conn->query("SELECT invoice_number, customer_name, total_amount, status FROM invoices LIMIT 5");
    if ($result) {
        $csvData = "Invoice Number,Customer,Amount,Status\n";
        while ($row = $result->fetch_assoc()) {
            $csvData .= implode(',', array_map(function($field) {
                return '"' . str_replace('"', '""', $field) . '"';
            }, $row)) . "\n";
        }
        
        $testExport = 'uploads/test_export.csv';
        if (file_put_contents($testExport, $csvData)) {
            echo "✅ CSV export test passed\n";
            unlink($testExport); // Clean up
        } else {
            echo "❌ CSV export test failed\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Export test error: " . $e->getMessage() . "\n";
}

// 5. Security Headers Test
echo "\n5. Security Headers Test\n";
echo "------------------------\n";

// Check if security functions exist
$securityFunctions = ['sanitizeInput', 'validateCSRF', 'requireLogin'];
foreach ($securityFunctions as $func) {
    if (function_exists($func)) {
        echo "✅ Security function '$func' exists\n";
    } else {
        echo "⚠️  Security function '$func' not found\n";
    }
}

// 6. Session Security Test
echo "\n6. Session Security Test\n";
echo "------------------------\n";

// Check session configuration
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "Session Configuration:\n";
echo "- Session name: " . session_name() . "\n";
echo "- Session ID: " . (session_id() ? "Active" : "Not started") . "\n";
echo "- Session save path: " . session_save_path() . "\n";

// 7. Database Security Test
echo "\n7. Database Security Test\n";
echo "-------------------------\n";

// Test prepared statements
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE status = ?");
    $status = 'active';
    $stmt->bind_param('s', $status);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result) {
        echo "✅ Prepared statements working\n";
    }
} catch (Exception $e) {
    echo "❌ Prepared statement error: " . $e->getMessage() . "\n";
}

// Check for SQL injection protection
echo "✅ Using MySQLi with prepared statements (SQL injection protection)\n";

echo "\n✅ File Management & Security test completed!\n";
?>
