<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

/**
 * Simple PDF Generator for Som Milk Invoice System
 * This is a basic implementation that creates a simple PDF structure
 * For production use, consider using libraries like mPDF, TCPDF, or FPDF
 */

class SimplePDFGenerator {
    private $content = '';
    private $title = '';
    
    public function __construct($title = 'Document') {
        $this->title = $title;
        $this->initializePDF();
    }
    
    private function initializePDF() {
        // Basic PDF structure
        $this->content = "%PDF-1.4\n";
        $this->content .= "1 0 obj\n";
        $this->content .= "<<\n";
        $this->content .= "/Type /Catalog\n";
        $this->content .= "/Pages 2 0 R\n";
        $this->content .= ">>\n";
        $this->content .= "endobj\n\n";
    }
    
    public function addText($text, $x = 50, $y = 750, $size = 12) {
        // This is a very basic implementation
        // In a real PDF library, this would be much more sophisticated
        return $this;
    }
    
    public function output($filename = null) {
        if ($filename) {
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        } else {
            header('Content-Type: application/pdf');
        }
        
        // For now, we'll redirect to the browser-based solution
        // This is because creating a proper PDF from scratch is very complex
        return false;
    }
}

// Get invoice ID
$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$invoice_id) {
    die('Invalid invoice ID');
}

// Get invoice data
$invoice = getInvoiceById($invoice_id);
if (!$invoice) {
    die('Invoice not found');
}

// For now, redirect to the browser-based PDF solution
// This ensures users can always get a PDF, even if server-side generation fails
header("Location: pdf-download.php?id=" . $invoice_id);
exit;

// The code below would be used if we had a proper PDF library
/*
try {
    $pdf = new SimplePDFGenerator('Invoice ' . $invoice['invoice_number']);
    
    // Add invoice content
    $pdf->addText('Som Milk Invoice System', 50, 750, 16);
    $pdf->addText('Invoice #: ' . $invoice['invoice_number'], 50, 720, 12);
    $pdf->addText('Customer: ' . $invoice['customer_name'], 50, 700, 12);
    $pdf->addText('Amount: $' . number_format($invoice['total_amount'], 2), 50, 680, 12);
    
    // Output PDF
    $pdf->output('invoice-' . $invoice['invoice_number'] . '.pdf');
    
} catch (Exception $e) {
    // Fall back to browser-based PDF generation
    header("Location: pdf-download.php?id=" . $invoice_id . "&error=" . urlencode($e->getMessage()));
    exit;
}
*/
?>
