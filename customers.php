<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Require login
requireLogin();

// Simple customer management - for now just show a placeholder
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - Som Milk Invoice Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Customer Management</h1>
                    <p class="text-gray-600">Manage your customer database</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <button class="inline-flex items-center px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Add Customer
                    </button>
                </div>
            </div>
        </div>

        <!-- Coming Soon Card -->
        <div class="bg-white rounded-lg shadow-md p-8 text-center">
            <div class="mb-6">
                <i class="fas fa-users text-6xl text-som-blue mb-4"></i>
                <h2 class="text-2xl font-bold text-gray-900 mb-2">Customer Management</h2>
                <p class="text-gray-600 mb-6">
                    Advanced customer management features are coming soon! For now, you can add customer information directly when creating invoices.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-gray-50 rounded-lg p-6">
                    <i class="fas fa-address-book text-2xl text-som-blue mb-3"></i>
                    <h3 class="font-semibold text-gray-900 mb-2">Customer Database</h3>
                    <p class="text-sm text-gray-600">Store and manage customer contact information</p>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <i class="fas fa-history text-2xl text-som-blue mb-3"></i>
                    <h3 class="font-semibold text-gray-900 mb-2">Invoice History</h3>
                    <p class="text-sm text-gray-600">View all invoices for each customer</p>
                </div>
                <div class="bg-gray-50 rounded-lg p-6">
                    <i class="fas fa-chart-line text-2xl text-som-blue mb-3"></i>
                    <h3 class="font-semibold text-gray-900 mb-2">Customer Analytics</h3>
                    <p class="text-sm text-gray-600">Track customer payment patterns and revenue</p>
                </div>
            </div>

            <div class="space-y-3">
                <a href="create-invoice.php" class="inline-flex items-center px-6 py-3 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Create Invoice with Customer Info
                </a>
                <div class="text-sm text-gray-500">
                    Customer information will be saved automatically when you create invoices
                </div>
            </div>
        </div>

        <!-- Sample Customers (from database) -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Sample Customers</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900">Diplomatic Hotel</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">+****************</p>
                </div>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900">Grand Restaurant</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">+****************</p>
                </div>
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900">City Cafe</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">+****************</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
</body>
</html>
