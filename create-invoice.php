<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Generate new invoice number
$invoice_number = generateInvoiceNumber();

// Get company information
$company_info = getCompanyInfo();

// Handle form submission
if ($_POST && isset($_POST['create_invoice'])) {
    $errors = [];
    
    // Validate required fields
    if (empty($_POST['customer_name'])) {
        $errors[] = "Customer name is required.";
    }
    
    if (empty($_POST['invoice_date'])) {
        $errors[] = "Invoice date is required.";
    }
    
    if (empty($_POST['items']) || !is_array($_POST['items'])) {
        $errors[] = "At least one item is required.";
    }
    
    // Validate items
    $items = [];
    $subtotal = 0;
    
    if (isset($_POST['items']) && is_array($_POST['items'])) {
        foreach ($_POST['items'] as $key => $item) {
            if (!empty($item['description']) && !empty($item['quantity']) && isset($item['unit_price'])) {
                $quantity = floatval($item['quantity']);
                $unit_price = floatval($item['unit_price']);
                $total_price = $quantity * $unit_price;
                
                $items[] = [
                    'description' => sanitizeInput($item['description']),
                    'quantity' => $quantity,
                    'unit_price' => $unit_price,
                    'total_price' => $total_price
                ];
                
                $subtotal += $total_price;
            }
        }
    }
    
    if (empty($items)) {
        $errors[] = "At least one valid item is required.";
    }
    
    // If no errors, create the invoice
    if (empty($errors)) {
        $tax_rate = floatval($_POST['tax_rate'] ?? 0);
        $tax_amount = $subtotal * ($tax_rate / 100);
        $total_amount = $subtotal + $tax_amount;
        
        $invoice_data = [
            'invoice_number' => sanitizeInput($_POST['invoice_number']),
            'invoice_date' => sanitizeInput($_POST['invoice_date']),
            'due_date' => sanitizeInput($_POST['due_date']),
            'customer_name' => sanitizeInput($_POST['customer_name']),
            'customer_email' => sanitizeInput($_POST['customer_email']),
            'customer_phone' => sanitizeInput($_POST['customer_phone']),
            'customer_address' => sanitizeInput($_POST['customer_address']),
            'subtotal' => $subtotal,
            'tax_amount' => $tax_amount,
            'total_amount' => $total_amount,
            'status' => 'pending',
            'notes' => sanitizeInput($_POST['notes']),
            'items' => $items
        ];
        
        $invoice_id = createInvoice($invoice_data);
        
        if ($invoice_id) {
            $_SESSION['success_message'] = "Invoice created successfully!";
            header("Location: view-invoice.php?id=" . $invoice_id);
            exit;
        } else {
            $errors[] = "Failed to create invoice. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Invoice - Som Milk Invoice Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Create New Invoice</h1>
                    <p class="text-gray-600">Fill in the details below to create a new invoice</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="invoices.php" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Invoices
                    </a>
                </div>
            </div>
        </div>

        <!-- Error Messages -->
        <?php if (!empty($errors)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <strong>Please fix the following errors:</strong>
                </div>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <!-- Invoice Form -->
        <form id="invoice-form" method="POST" action="process-invoice.php" class="space-y-8">
            <input type="hidden" name="create_invoice" value="1">
            
            <!-- Invoice Header -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Invoice Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Invoice Number -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Invoice Number</label>
                        <input type="text" name="invoice_number" value="<?php echo htmlspecialchars($invoice_number); ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Invoice Date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Invoice Date</label>
                        <input type="date" name="invoice_date" id="invoice_date" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Due Date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                        <input type="date" name="due_date" id="due_date"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                    </div>
                    
                    <!-- Tax Rate -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tax Rate (%)</label>
                        <input type="number" name="tax_rate" id="tax_rate" value="0" min="0" max="100" step="0.01"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                               onchange="calculateInvoiceTotal()">
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Customer Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Customer Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Customer Name *</label>
                        <input type="text" name="customer_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                               placeholder="e.g., Diplomatic Hotel">
                    </div>
                    
                    <!-- Customer Email -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" name="customer_email"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                               placeholder="<EMAIL>">
                    </div>
                    
                    <!-- Customer Phone -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                        <input type="tel" name="customer_phone"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                               placeholder="+****************">
                    </div>
                    
                    <!-- Customer Address -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <textarea name="customer_address" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                  placeholder="Customer address"></textarea>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Invoice Items</h2>
                    <button type="button" onclick="addInvoiceItem()" 
                            class="px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Add Item
                    </button>
                </div>
                
                <div id="invoice-items" class="space-y-4">
                    <!-- Items will be added here by JavaScript -->
                </div>
            </div>

            <!-- Invoice Summary -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">Invoice Summary</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <!-- Notes -->
                        <label class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                        <textarea name="notes" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                  placeholder="Additional notes or terms..."></textarea>
                    </div>
                    
                    <div>
                        <!-- Totals -->
                        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Subtotal:</span>
                                <span id="subtotal-display" class="font-medium">$0.00</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Tax:</span>
                                <span id="tax-display" class="font-medium">$0.00</span>
                            </div>
                            <div class="border-t pt-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-lg font-semibold text-gray-900">Total:</span>
                                    <span id="total-display" class="text-lg font-bold text-som-blue">$0.00</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hidden fields for totals -->
                        <input type="hidden" name="subtotal" id="subtotal" value="0">
                        <input type="hidden" name="tax_amount" id="tax_amount" value="0">
                        <input type="hidden" name="total_amount" id="total_amount" value="0">
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex flex-col sm:flex-row gap-4 justify-end">
                <a href="invoices.php" 
                   class="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition duration-200 text-center">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-6 py-3 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>Create Invoice
                </button>
            </div>
        </form>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
</body>
</html>
