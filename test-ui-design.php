<?php
require_once 'includes/config.php';

echo "UI/UX & Responsive Design Test\n";
echo "===============================\n\n";

// 1. Check key UI files
echo "1. UI Files Test\n";
echo "----------------\n";
$uiFiles = [
    'index.php' => 'Dashboard',
    'login.php' => 'Login Page',
    'create-invoice.php' => 'Invoice Creation',
    'invoices.php' => 'Invoice List',
    'customers.php' => 'Customer Management',
    'users.php' => 'User Management',
    'includes/navbar.php' => 'Navigation Bar',
    'includes/footer.php' => 'Footer'
];

foreach ($uiFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description ($file) exists\n";
    } else {
        echo "❌ $description ($file) missing\n";
    }
}

// 2. Check CSS and JS assets
echo "\n2. Assets Test\n";
echo "--------------\n";
$assets = [
    'assets/css/style.css' => 'Main CSS',
    'assets/js/main.js' => 'Main JavaScript',
    'assets/js/invoice.js' => 'Invoice JavaScript'
];

foreach ($assets as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "✅ $description ($file) - " . number_format($size) . " bytes\n";
    } else {
        echo "⚠️  $description ($file) not found (may use CDN)\n";
    }
}

// 3. Check Tailwind CSS usage
echo "\n3. Tailwind CSS Integration Test\n";
echo "--------------------------------\n";
$filesToCheck = ['index.php', 'login.php', 'create-invoice.php'];
$tailwindFound = false;

foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'tailwindcss.com') !== false || strpos($content, 'tailwind') !== false) {
            echo "✅ $file uses Tailwind CSS\n";
            $tailwindFound = true;
        }
    }
}

if ($tailwindFound) {
    echo "✅ Tailwind CSS integration confirmed\n";
} else {
    echo "⚠️  Tailwind CSS integration not detected\n";
}

// 4. Check Som Milk branding
echo "\n4. Som Milk Branding Test\n";
echo "-------------------------\n";
$brandingElements = [
    'Som Milk' => 'Company name',
    '#2563EB' => 'Primary blue color',
    '#059669' => 'Secondary green color',
    'Inter' => 'Font family'
];

$brandingFound = [];
foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        foreach ($brandingElements as $element => $description) {
            if (stripos($content, $element) !== false) {
                $brandingFound[$element] = true;
            }
        }
    }
}

foreach ($brandingElements as $element => $description) {
    if (isset($brandingFound[$element])) {
        echo "✅ $description found in templates\n";
    } else {
        echo "⚠️  $description not found in checked files\n";
    }
}

// 5. Check responsive design elements
echo "\n5. Responsive Design Test\n";
echo "-------------------------\n";
$responsiveElements = [
    'viewport' => 'meta name="viewport"',
    'responsive' => 'responsive',
    'mobile' => 'mobile',
    'md:' => 'Tailwind medium breakpoint',
    'lg:' => 'Tailwind large breakpoint'
];

$responsiveFound = [];
foreach ($filesToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        foreach ($responsiveElements as $key => $element) {
            if (stripos($content, $element) !== false) {
                $responsiveFound[$key] = true;
            }
        }
    }
}

foreach ($responsiveElements as $key => $description) {
    if (isset($responsiveFound[$key])) {
        echo "✅ $description found\n";
    } else {
        echo "⚠️  $description not found\n";
    }
}

// 6. Check navigation structure
echo "\n6. Navigation Structure Test\n";
echo "----------------------------\n";
if (file_exists('includes/navbar.php')) {
    $navbar = file_get_contents('includes/navbar.php');
    $navItems = [
        'Dashboard' => 'dashboard link',
        'Invoices' => 'invoices link',
        'Customers' => 'customers link',
        'Users' => 'users link',
        'Logout' => 'logout link'
    ];
    
    foreach ($navItems as $item => $description) {
        if (stripos($navbar, $item) !== false) {
            echo "✅ $description found in navigation\n";
        } else {
            echo "⚠️  $description not found in navigation\n";
        }
    }
} else {
    echo "❌ Navigation file not found\n";
}

// 7. Check form validation
echo "\n7. Form Validation Test\n";
echo "-----------------------\n";
$formsToCheck = ['create-invoice.php', 'login.php'];
$validationFound = false;

foreach ($formsToCheck as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'required') !== false || strpos($content, 'validation') !== false) {
            echo "✅ $file has form validation\n";
            $validationFound = true;
        }
    }
}

if (!$validationFound) {
    echo "⚠️  Form validation not detected in checked files\n";
}

echo "\n✅ UI/UX & Responsive Design test completed!\n";
?>
