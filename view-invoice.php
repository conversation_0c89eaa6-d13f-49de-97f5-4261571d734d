<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get invoice ID
$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$invoice_id) {
    header('Location: invoices.php');
    exit;
}

// Get invoice data
$invoice = getInvoiceById($invoice_id);
if (!$invoice) {
    $_SESSION['error_message'] = "Invoice not found.";
    header('Location: invoices.php');
    exit;
}

// Get company information
$company_info = getCompanyInfo();

// Handle status updates
if ($_POST && isset($_POST['update_status'])) {
    $new_status = sanitizeInput($_POST['status']);
    
    if (updateInvoiceStatus($invoice_id, $new_status)) {
        $_SESSION['success_message'] = "Invoice status updated successfully!";
        header("Location: view-invoice.php?id=" . $invoice_id);
        exit;
    } else {
        $error_message = "Failed to update invoice status.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?> - Som Milk</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?></h1>
                    <p class="text-gray-600">View and manage invoice details</p>
                </div>
                <div class="mt-4 md:mt-0 flex flex-wrap gap-2">
                    <a href="invoices.php" class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Invoices
                    </a>
                    <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition duration-200">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                    <a href="generate-pdf.php?id=<?php echo $invoice['id']; ?>" target="_blank" class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition duration-200">
                        <i class="fas fa-file-pdf mr-2"></i>View PDF
                    </a>
                    <a href="generate-pdf-server.php?id=<?php echo $invoice['id']; ?>" class="inline-flex items-center px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-download mr-2"></i>Download PDF
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Invoice Content -->
            <div class="lg:col-span-2">
                <!-- Invoice Header -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex justify-between items-start mb-6">
                        <div>
                            <h2 class="text-2xl font-bold text-som-blue mb-2"><?php echo htmlspecialchars($company_info['company_name']); ?></h2>
                            <div class="text-gray-600">
                                <?php if ($company_info['address']): ?>
                                    <?php echo htmlspecialchars($company_info['address']); ?><br>
                                <?php endif; ?>
                                <?php if ($company_info['city'] || $company_info['state'] || $company_info['zip_code']): ?>
                                    <?php echo htmlspecialchars($company_info['city']); ?>
                                    <?php echo $company_info['state'] ? ', ' . htmlspecialchars($company_info['state']) : ''; ?>
                                    <?php echo htmlspecialchars($company_info['zip_code']); ?><br>
                                <?php endif; ?>
                                <?php if ($company_info['phone']): ?>
                                    Phone: <?php echo htmlspecialchars($company_info['phone']); ?><br>
                                <?php endif; ?>
                                <?php if ($company_info['email']): ?>
                                    Email: <?php echo htmlspecialchars($company_info['email']); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="text-right">
                            <h3 class="text-3xl font-bold text-som-blue mb-2">INVOICE</h3>
                            <div class="text-lg font-semibold mb-1"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                            <div class="text-gray-600">
                                <strong>Date:</strong> <?php echo date('F j, Y', strtotime($invoice['invoice_date'])); ?><br>
                                <?php if ($invoice['due_date']): ?>
                                    <strong>Due:</strong> <?php echo date('F j, Y', strtotime($invoice['due_date'])); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-semibold text-som-blue mb-3">Bill To:</h4>
                        <div class="text-gray-700">
                            <div class="font-semibold text-lg"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                            <?php if ($invoice['customer_address']): ?>
                                <div class="mt-1"><?php echo nl2br(htmlspecialchars($invoice['customer_address'])); ?></div>
                            <?php endif; ?>
                            <?php if ($invoice['customer_email']): ?>
                                <div class="mt-1">Email: <?php echo htmlspecialchars($invoice['customer_email']); ?></div>
                            <?php endif; ?>
                            <?php if ($invoice['customer_phone']): ?>
                                <div class="mt-1">Phone: <?php echo htmlspecialchars($invoice['customer_phone']); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Invoice Items</h4>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="bg-som-blue text-white">
                                    <th class="px-4 py-3 text-left">Description</th>
                                    <th class="px-4 py-3 text-center">Qty</th>
                                    <th class="px-4 py-3 text-right">Unit Price</th>
                                    <th class="px-4 py-3 text-right">Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($invoice['items'] as $item): ?>
                                    <tr class="border-b border-gray-200">
                                        <td class="px-4 py-3"><?php echo htmlspecialchars($item['description']); ?></td>
                                        <td class="px-4 py-3 text-center"><?php echo number_format($item['quantity'], 2); ?></td>
                                        <td class="px-4 py-3 text-right">$<?php echo number_format($item['unit_price'], 2); ?></td>
                                        <td class="px-4 py-3 text-right font-semibold">$<?php echo number_format($item['total_price'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Totals -->
                    <div class="mt-6 flex justify-end">
                        <div class="w-64">
                            <div class="bg-gray-50 rounded-lg p-4 space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal:</span>
                                    <span class="font-medium">$<?php echo number_format($invoice['subtotal'], 2); ?></span>
                                </div>
                                <?php if ($invoice['tax_amount'] > 0): ?>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Tax:</span>
                                        <span class="font-medium">$<?php echo number_format($invoice['tax_amount'], 2); ?></span>
                                    </div>
                                <?php endif; ?>
                                <div class="border-t pt-2">
                                    <div class="flex justify-between">
                                        <span class="text-lg font-semibold">Total:</span>
                                        <span class="text-lg font-bold text-som-blue">$<?php echo number_format($invoice['total_amount'], 2); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <?php if ($invoice['notes']): ?>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Notes</h4>
                        <div class="text-gray-700 whitespace-pre-line"><?php echo htmlspecialchars($invoice['notes']); ?></div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Status Card -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Invoice Status</h4>
                    
                    <?php
                    $status_colors = [
                        'draft' => 'bg-gray-100 text-gray-800',
                        'pending' => 'bg-yellow-100 text-yellow-800',
                        'paid' => 'bg-green-100 text-green-800',
                        'overdue' => 'bg-red-100 text-red-800',
                        'cancelled' => 'bg-red-100 text-red-800'
                    ];
                    $status_class = $status_colors[$invoice['status']] ?? 'bg-gray-100 text-gray-800';
                    ?>
                    
                    <div class="mb-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo $status_class; ?>">
                            <?php echo ucfirst($invoice['status']); ?>
                        </span>
                    </div>

                    <!-- Status Update Form -->
                    <form method="POST" class="space-y-3">
                        <input type="hidden" name="update_status" value="1">
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                            <option value="draft" <?php echo $invoice['status'] == 'draft' ? 'selected' : ''; ?>>Draft</option>
                            <option value="pending" <?php echo $invoice['status'] == 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="paid" <?php echo $invoice['status'] == 'paid' ? 'selected' : ''; ?>>Paid</option>
                            <option value="overdue" <?php echo $invoice['status'] == 'overdue' ? 'selected' : ''; ?>>Overdue</option>
                            <option value="cancelled" <?php echo $invoice['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                        <button type="submit" class="w-full px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-md transition duration-200">
                            Update Status
                        </button>
                    </form>
                </div>

                <!-- Invoice Details -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4">Invoice Details</h4>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Created:</span>
                            <span><?php echo date('M j, Y', strtotime($invoice['created_at'])); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Updated:</span>
                            <span><?php echo date('M j, Y', strtotime($invoice['updated_at'])); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Items:</span>
                            <span><?php echo count($invoice['items']); ?></span>
                        </div>
                        <?php if ($invoice['due_date']): ?>
                            <?php
                            $days_until_due = (strtotime($invoice['due_date']) - time()) / (60 * 60 * 24);
                            $days_until_due = floor($days_until_due);
                            ?>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Due in:</span>
                                <span class="<?php echo $days_until_due < 0 ? 'text-red-600 font-semibold' : ($days_until_due <= 7 ? 'text-yellow-600' : 'text-green-600'); ?>">
                                    <?php 
                                    if ($days_until_due < 0) {
                                        echo abs($days_until_due) . ' days overdue';
                                    } elseif ($days_until_due == 0) {
                                        echo 'Due today';
                                    } else {
                                        echo $days_until_due . ' days';
                                    }
                                    ?>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Banking Information -->
                <?php if ($company_info['bank_name'] || $company_info['account_number']): ?>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h4>
                        <div class="space-y-2 text-sm">
                            <?php if ($company_info['bank_name']): ?>
                                <div><strong>Bank:</strong> <?php echo htmlspecialchars($company_info['bank_name']); ?></div>
                            <?php endif; ?>
                            <?php if ($company_info['account_number']): ?>
                                <div><strong>Account:</strong> <?php echo htmlspecialchars($company_info['account_number']); ?></div>
                            <?php endif; ?>
                            <?php if ($company_info['routing_number']): ?>
                                <div><strong>Routing:</strong> <?php echo htmlspecialchars($company_info['routing_number']); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
</body>
</html>
