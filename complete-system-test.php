<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "Som Milk Complete System Test\n";
echo "==============================\n\n";

$testResults = [];

// 1. Database Connection Test
echo "1. Database Connection Test\n";
echo "---------------------------\n";
if ($conn->connect_error) {
    echo "❌ Database connection failed\n";
    $testResults['database'] = false;
} else {
    echo "✅ Database connection successful\n";
    $testResults['database'] = true;
}

// 2. Table Structure Test
echo "\n2. Table Structure Test\n";
echo "-----------------------\n";
$requiredTables = ['admin_users', 'users', 'customers', 'invoices', 'invoice_items', 'products', 'company_info'];
$tablesExist = true;

foreach ($requiredTables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result && $result->num_rows > 0) {
        echo "✅ Table '$table' exists\n";
    } else {
        echo "❌ Table '$table' missing\n";
        $tablesExist = false;
    }
}
$testResults['tables'] = $tablesExist;

// 3. Sample Data Test
echo "\n3. Sample Data Test\n";
echo "-------------------\n";

// Add sample products if none exist
$result = $conn->query('SELECT COUNT(*) as count FROM products');
if ($result) {
    $row = $result->fetch_assoc();
    if ($row['count'] == 0) {
        echo "Adding sample products...\n";
        $products = [
            ['Whole Milk - 1 Gallon', 'Fresh whole milk', 4.99, 'Dairy', 'SM-001'],
            ['2% Milk - 1 Gallon', 'Reduced fat milk', 4.79, 'Dairy', 'SM-002'],
            ['Organic Milk - 1 Gallon', 'Organic whole milk', 6.99, 'Dairy', 'SM-003']
        ];
        
        $stmt = $conn->prepare('INSERT INTO products (name, description, unit_price, category, sku, active) VALUES (?, ?, ?, ?, ?, 1)');
        foreach ($products as $product) {
            $stmt->bind_param('ssdss', $product[0], $product[1], $product[2], $product[3], $product[4]);
            $stmt->execute();
        }
        echo "✅ Sample products added\n";
    } else {
        echo "✅ Products exist: " . $row['count'] . " products\n";
    }
}

// 4. Authentication Test
echo "\n4. Authentication System Test\n";
echo "-----------------------------\n";
$result = $conn->query("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
if ($result && $result->fetch_assoc()['count'] > 0) {
    echo "✅ Admin user exists\n";
    $testResults['auth'] = true;
} else {
    echo "❌ Admin user missing\n";
    $testResults['auth'] = false;
}

// 5. File Permissions Test
echo "\n5. File Permissions Test\n";
echo "------------------------\n";
$directories = ['uploads/', 'uploads/invoices/', 'uploads/backups/'];
$permissionsOk = true;

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Created directory: $dir\n";
    }
    
    if (is_writable($dir)) {
        echo "✅ $dir is writable\n";
    } else {
        echo "❌ $dir is not writable\n";
        $permissionsOk = false;
    }
}
$testResults['permissions'] = $permissionsOk;

// 6. Core Functions Test
echo "\n6. Core Functions Test\n";
echo "----------------------\n";
$functions = ['generateInvoiceNumber', 'getCompanyInfo', 'getUserStats'];
$functionsOk = true;

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "✅ Function '$func' exists\n";
    } else {
        echo "❌ Function '$func' missing\n";
        $functionsOk = false;
    }
}
$testResults['functions'] = $functionsOk;

// 7. Configuration Test
echo "\n7. Configuration Test\n";
echo "---------------------\n";
$constants = ['APP_NAME', 'BASE_URL', 'COMPANY_NAME'];
$configOk = true;

foreach ($constants as $const) {
    if (defined($const)) {
        echo "✅ Constant '$const' defined: " . constant($const) . "\n";
    } else {
        echo "❌ Constant '$const' not defined\n";
        $configOk = false;
    }
}
$testResults['config'] = $configOk;

// 8. Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "SYSTEM TEST SUMMARY\n";
echo str_repeat("=", 50) . "\n";

$totalTests = count($testResults);
$passedTests = array_sum($testResults);

foreach ($testResults as $test => $result) {
    $status = $result ? "✅ PASS" : "❌ FAIL";
    echo sprintf("%-15s: %s\n", ucfirst($test), $status);
}

echo "\nOverall Result: $passedTests/$totalTests tests passed\n";

if ($passedTests === $totalTests) {
    echo "🎉 ALL TESTS PASSED! Som Milk system is ready!\n";
} else {
    echo "⚠️  Some tests failed. Please review the issues above.\n";
}

echo str_repeat("=", 50) . "\n";
?>
