<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = "Backup & Export";

// Handle backup/export requests
if ($_POST) {
    if (isset($_POST['backup_database'])) {
        $backup_result = createDatabaseBackup();
        if ($backup_result['success']) {
            $message = urlencode("Database backup created successfully: " . $backup_result['filename']);
            header("Location: backup-export.php?success=" . $message);
        } else {
            $message = urlencode("Failed to create database backup: " . $backup_result['error']);
            header("Location: backup-export.php?error=" . $message);
        }
        exit;
    }
    
    if (isset($_POST['export_invoices'])) {
        header("Location: export-invoices.php?export=csv");
        exit;
    }
    
    if (isset($_POST['export_customers'])) {
        header("Location: export-customers.php?export=csv");
        exit;
    }
}

// Get backup files
$backup_files = getBackupFiles();
$company_info = getCompanyInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Som Milk Invoice Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#2563EB',
                        'som-light-blue': '#3B82F6',
                        'som-green': '#059669',
                        'som-light-green': '#10B981',
                        'som-gray': '#F8FAFC',
                        'som-dark': '#1E293B',
                        'som-red': '#DC2626',
                        'som-border': '#E2E8F0'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-som-gray">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-som-dark mb-2">Backup & Export</h1>
            <p class="text-gray-600">Manage database backups and export your data</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($_GET['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($_GET['error']); ?>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Database Backup Section -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-6">
                    <div class="p-3 bg-blue-100 rounded-full mr-4">
                        <i class="fas fa-database text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-som-dark">Database Backup</h2>
                        <p class="text-gray-600">Create and manage database backups</p>
                    </div>
                </div>

                <form method="POST" class="mb-6">
                    <button type="submit" name="backup_database" 
                            class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-3 px-4 rounded-md transition-colors">
                        <i class="fas fa-download mr-2"></i>Create Database Backup
                    </button>
                </form>

                <!-- Backup Files List -->
                <div class="border-t pt-6">
                    <h3 class="text-lg font-medium text-som-dark mb-4">Recent Backups</h3>
                    
                    <?php if (empty($backup_files)): ?>
                        <p class="text-gray-500 text-center py-4">No backup files found</p>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($backup_files as $file): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-file-archive text-gray-600 mr-3"></i>
                                        <div>
                                            <p class="font-medium text-som-dark"><?php echo htmlspecialchars($file['name']); ?></p>
                                            <p class="text-sm text-gray-500">
                                                <?php echo date('M j, Y g:i A', $file['modified']); ?> • 
                                                <?php echo formatFileSize($file['size']); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <a href="download-backup.php?file=<?php echo urlencode($file['name']); ?>" 
                                           class="text-som-blue hover:text-som-light-blue text-sm">
                                            <i class="fas fa-download mr-1"></i>Download
                                        </a>
                                        <a href="delete-backup.php?file=<?php echo urlencode($file['name']); ?>" 
                                           class="text-som-red hover:text-red-700 text-sm"
                                           onclick="return confirm('Are you sure you want to delete this backup?')">
                                            <i class="fas fa-trash mr-1"></i>Delete
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Data Export Section -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-6">
                    <div class="p-3 bg-green-100 rounded-full mr-4">
                        <i class="fas fa-file-export text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-som-dark">Data Export</h2>
                        <p class="text-gray-600">Export your data to CSV format</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <!-- Export Invoices -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-invoice text-som-blue mr-3"></i>
                                <div>
                                    <h3 class="font-medium text-som-dark">Export Invoices</h3>
                                    <p class="text-sm text-gray-600">Export all invoice data to CSV</p>
                                </div>
                            </div>
                            <form method="POST" class="inline">
                                <button type="submit" name="export_invoices" 
                                        class="bg-som-green hover:bg-som-light-green text-white px-4 py-2 rounded-md text-sm transition-colors">
                                    <i class="fas fa-download mr-1"></i>Export
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Export Customers -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-users text-som-blue mr-3"></i>
                                <div>
                                    <h3 class="font-medium text-som-dark">Export Customers</h3>
                                    <p class="text-sm text-gray-600">Export all customer data to CSV</p>
                                </div>
                            </div>
                            <form method="POST" class="inline">
                                <button type="submit" name="export_customers" 
                                        class="bg-som-green hover:bg-som-light-green text-white px-4 py-2 rounded-md text-sm transition-colors">
                                    <i class="fas fa-download mr-1"></i>Export
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Export Company Data -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-building text-som-blue mr-3"></i>
                                <div>
                                    <h3 class="font-medium text-som-dark">Export Company Settings</h3>
                                    <p class="text-sm text-gray-600">Export company information and settings</p>
                                </div>
                            </div>
                            <a href="export-company.php?export=csv" 
                               class="bg-som-green hover:bg-som-light-green text-white px-4 py-2 rounded-md text-sm transition-colors inline-block">
                                <i class="fas fa-download mr-1"></i>Export
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Schedule Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-8">
            <div class="flex items-center mb-6">
                <div class="p-3 bg-purple-100 rounded-full mr-4">
                    <i class="fas fa-clock text-purple-600 text-xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-som-dark">Backup Schedule</h2>
                    <p class="text-gray-600">Automated backup recommendations</p>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center p-4 border border-gray-200 rounded-lg">
                    <i class="fas fa-calendar-day text-som-blue text-2xl mb-3"></i>
                    <h3 class="font-medium text-som-dark mb-2">Daily Backups</h3>
                    <p class="text-sm text-gray-600">Recommended for active businesses</p>
                </div>
                
                <div class="text-center p-4 border border-gray-200 rounded-lg">
                    <i class="fas fa-calendar-week text-som-green text-2xl mb-3"></i>
                    <h3 class="font-medium text-som-dark mb-2">Weekly Backups</h3>
                    <p class="text-sm text-gray-600">Good for moderate usage</p>
                </div>
                
                <div class="text-center p-4 border border-gray-200 rounded-lg">
                    <i class="fas fa-calendar-alt text-som-red text-2xl mb-3"></i>
                    <h3 class="font-medium text-som-dark mb-2">Monthly Backups</h3>
                    <p class="text-sm text-gray-600">Minimum recommended frequency</p>
                </div>
            </div>

            <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                    <span class="text-sm text-yellow-800 font-medium">Backup Recommendations</span>
                </div>
                <ul class="text-sm text-yellow-700 mt-2 list-disc list-inside space-y-1">
                    <li>Store backups in multiple locations (local and cloud)</li>
                    <li>Test backup restoration regularly</li>
                    <li>Keep at least 3 recent backups</li>
                    <li>Document your backup and recovery procedures</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
