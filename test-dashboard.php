<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "Testing Dashboard & Analytics\n";
echo "=============================\n\n";

// Test dashboard statistics
echo "1. Dashboard Statistics:\n";
echo "------------------------\n";

try {
    // Test monthly revenue
    $monthlyRevenue = getMonthlyRevenue();
    echo "✅ Monthly Revenue Data: " . count($monthlyRevenue) . " months\n";
    if (!empty($monthlyRevenue)) {
        $latestMonth = end($monthlyRevenue);
        echo "   Latest: " . $latestMonth['month'] . " - $" . number_format($latestMonth['revenue'], 2) . "\n";
    }
} catch (Exception $e) {
    echo "❌ Monthly Revenue Error: " . $e->getMessage() . "\n";
}

try {
    // Test invoice status stats
    $statusStats = getInvoiceStatusStats();
    echo "✅ Invoice Status Stats: " . count($statusStats) . " status types\n";
    foreach ($statusStats as $stat) {
        echo "   " . ucfirst($stat['status']) . ": " . $stat['count'] . " invoices\n";
    }
} catch (Exception $e) {
    echo "❌ Invoice Status Stats Error: " . $e->getMessage() . "\n";
}

try {
    // Test user stats
    $userStats = getUserStats();
    echo "✅ User Statistics: Total " . ($userStats['total'] ?? 0) . " users\n";
    if (isset($userStats['by_type'])) {
        foreach ($userStats['by_type'] as $type => $count) {
            echo "   " . ucfirst($type) . ": $count users\n";
        }
    }
} catch (Exception $e) {
    echo "❌ User Stats Error: " . $e->getMessage() . "\n";
}

// Test dashboard totals
echo "\n2. Dashboard Totals:\n";
echo "--------------------\n";

// Total invoices
$result = $conn->query("SELECT COUNT(*) as count, SUM(total_amount) as total FROM invoices");
if ($result) {
    $row = $result->fetch_assoc();
    echo "✅ Total Invoices: " . $row['count'] . " (Total Value: $" . number_format($row['total'], 2) . ")\n";
}

// Total customers
$result = $conn->query("SELECT COUNT(*) as count FROM customers WHERE status = 'active'");
if ($result) {
    $row = $result->fetch_assoc();
    echo "✅ Active Customers: " . $row['count'] . "\n";
}

// Total products
$result = $conn->query("SELECT COUNT(*) as count FROM products WHERE active = 1");
if ($result) {
    $row = $result->fetch_assoc();
    echo "✅ Active Products: " . $row['count'] . "\n";
}

// Recent activity
echo "\n3. Recent Activity:\n";
echo "-------------------\n";
$result = $conn->query("SELECT invoice_number, customer_name, total_amount, status, created_at FROM invoices ORDER BY created_at DESC LIMIT 5");
if ($result) {
    echo "Recent Invoices:\n";
    while ($row = $result->fetch_assoc()) {
        echo "   • " . $row['invoice_number'] . " - " . $row['customer_name'] . " ($" . $row['total_amount'] . ") - " . $row['status'] . "\n";
    }
}

echo "\n✅ Dashboard & Analytics test completed!\n";
?>
