<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "Error Handling & Validation Test\n";
echo "================================\n\n";

// 1. Database Error Handling Test
echo "1. Database Error Handling Test\n";
echo "-------------------------------\n";

// Test invalid query
$result = $conn->query("SELECT * FROM non_existent_table");
if (!$result) {
    echo "✅ Database error properly caught: " . $conn->error . "\n";
} else {
    echo "❌ Database error not handled\n";
}

// 2. Input Validation Test
echo "\n2. Input Validation Test\n";
echo "------------------------\n";

// Test sanitizeInput function
if (function_exists('sanitizeInput')) {
    $testInputs = [
        '<script>alert("xss")</script>' => 'XSS attempt',
        "'; DROP TABLE users; --" => 'SQL injection attempt',
        '   test   ' => 'Whitespace trimming'
    ];
    
    foreach ($testInputs as $input => $description) {
        $sanitized = sanitizeInput($input);
        if ($sanitized !== $input) {
            echo "✅ $description properly sanitized\n";
        } else {
            echo "⚠️  $description may not be properly sanitized\n";
        }
    }
} else {
    echo "❌ sanitizeInput function not found\n";
}

// 3. Authentication Error Handling
echo "\n3. Authentication Error Handling\n";
echo "--------------------------------\n";

// Test login with invalid credentials
try {
    $stmt = $conn->prepare("SELECT id, username, password_hash FROM admin_users WHERE username = ?");
    $username = 'invalid_user';
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo "✅ Invalid username properly handled\n";
    } else {
        echo "❌ Invalid username not properly handled\n";
    }
} catch (Exception $e) {
    echo "✅ Authentication error caught: " . $e->getMessage() . "\n";
}

// 4. File Upload Validation
echo "\n4. File Upload Validation\n";
echo "-------------------------\n";

// Test file extension validation
$allowedExtensions = ['pdf', 'jpg', 'jpeg', 'png'];
$testFiles = [
    'document.pdf' => true,
    'image.jpg' => true,
    'script.php' => false,
    'malware.exe' => false
];

foreach ($testFiles as $filename => $shouldBeAllowed) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    $isAllowed = in_array($extension, $allowedExtensions);
    
    if ($isAllowed === $shouldBeAllowed) {
        $status = $shouldBeAllowed ? "allowed" : "blocked";
        echo "✅ $filename correctly $status\n";
    } else {
        echo "❌ $filename validation failed\n";
    }
}

// 5. Form Validation Test
echo "\n5. Form Validation Test\n";
echo "-----------------------\n";

// Test email validation
$testEmails = [
    '<EMAIL>' => true,
    'invalid.email' => false,
    'test@' => false,
    '@domain.com' => false
];

foreach ($testEmails as $email => $shouldBeValid) {
    $isValid = filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    
    if ($isValid === $shouldBeValid) {
        $status = $shouldBeValid ? "valid" : "invalid";
        echo "✅ $email correctly identified as $status\n";
    } else {
        echo "❌ $email validation failed\n";
    }
}

// 6. Numeric Validation Test
echo "\n6. Numeric Validation Test\n";
echo "--------------------------\n";

$testNumbers = [
    '123.45' => true,
    'abc' => false,
    '0' => true,
    '-50.25' => true,
    '1,234.56' => false // Contains comma
];

foreach ($testNumbers as $number => $shouldBeValid) {
    $isValid = is_numeric($number);
    
    if ($isValid === $shouldBeValid) {
        $status = $shouldBeValid ? "valid" : "invalid";
        echo "✅ '$number' correctly identified as $status number\n";
    } else {
        echo "❌ '$number' validation failed\n";
    }
}

// 7. Date Validation Test
echo "\n7. Date Validation Test\n";
echo "-----------------------\n";

$testDates = [
    '2024-12-31' => true,
    '2024-02-30' => false, // Invalid date
    'invalid-date' => false,
    '12/31/2024' => false // Wrong format
];

foreach ($testDates as $date => $shouldBeValid) {
    $timestamp = strtotime($date);
    $isValid = ($timestamp !== false) && (date('Y-m-d', $timestamp) === $date);
    
    if ($isValid === $shouldBeValid) {
        $status = $shouldBeValid ? "valid" : "invalid";
        echo "✅ '$date' correctly identified as $status\n";
    } else {
        echo "❌ '$date' validation failed\n";
    }
}

// 8. Error Logging Test
echo "\n8. Error Logging Test\n";
echo "---------------------\n";

// Check if error logging is enabled
if (ini_get('log_errors')) {
    echo "✅ Error logging is enabled\n";
    echo "   Log file: " . (ini_get('error_log') ?: 'system default') . "\n";
} else {
    echo "⚠️  Error logging is disabled\n";
}

// 9. Exception Handling Test
echo "\n9. Exception Handling Test\n";
echo "--------------------------\n";

try {
    // Test division by zero
    $result = 10 / 0;
    echo "❌ Division by zero not caught\n";
} catch (DivisionByZeroError $e) {
    echo "✅ Division by zero properly caught\n";
} catch (Exception $e) {
    echo "✅ Exception caught: " . $e->getMessage() . "\n";
}

echo "\n✅ Error Handling & Validation test completed!\n";
?>
