<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is logged in (you can implement this later)
// For now, we'll assume access is allowed

$page_title = "Invoice Templates";

// Handle template selection
if ($_POST && isset($_POST['set_default_template'])) {
    $template_id = (int)$_POST['template_id'];
    
    // Update company settings with selected template
    $stmt = $conn->prepare("UPDATE company_info SET invoice_template = ? WHERE id = 1");
    $stmt->bind_param("i", $template_id);
    
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Default template updated successfully!";
    } else {
        $_SESSION['error_message'] = "Failed to update template.";
    }
    
    header("Location: invoice-templates.php");
    exit;
}

// Get current template setting
$company_info = getCompanyInfo();
$current_template = $company_info['invoice_template'] ?? 1;

// Available templates
$templates = [
    1 => [
        'name' => 'Professional Blue',
        'description' => 'Clean professional design with blue accents',
        'preview' => 'assets/images/template-1-preview.png',
        'primary_color' => '#1e40af',
        'secondary_color' => '#3b82f6'
    ],
    2 => [
        'name' => 'Modern Green',
        'description' => 'Modern design with green color scheme',
        'preview' => 'assets/images/template-2-preview.png',
        'primary_color' => '#059669',
        'secondary_color' => '#10b981'
    ],
    3 => [
        'name' => 'Classic Gray',
        'description' => 'Traditional layout with gray tones',
        'preview' => 'assets/images/template-3-preview.png',
        'primary_color' => '#374151',
        'secondary_color' => '#6b7280'
    ],
    4 => [
        'name' => 'Elegant Purple',
        'description' => 'Sophisticated design with purple accents',
        'preview' => 'assets/images/template-4-preview.png',
        'primary_color' => '#7c3aed',
        'secondary_color' => '#8b5cf6'
    ]
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Som Milk Invoice System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#2563EB',
                        'som-light-blue': '#3B82F6',
                        'som-green': '#059669',
                        'som-light-green': '#10B981',
                        'som-gray': '#F8FAFC',
                        'som-dark': '#1E293B',
                        'som-red': '#DC2626',
                        'som-border': '#E2E8F0'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-som-gray min-h-screen">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mx-auto px-4 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-som-dark">Invoice Templates</h1>
            <p class="text-gray-600 mt-2">Choose and customize your invoice template design</p>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Template Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            <?php foreach ($templates as $id => $template): ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden <?php echo $current_template == $id ? 'ring-2 ring-som-blue' : ''; ?>">
                    <!-- Template Preview -->
                    <div class="relative">
                        <div class="h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <div class="text-center">
                                <div class="w-16 h-16 mx-auto mb-3 rounded-lg flex items-center justify-center" 
                                     style="background-color: <?php echo $template['primary_color']; ?>">
                                    <i class="fas fa-file-invoice text-white text-2xl"></i>
                                </div>
                                <div class="text-sm font-medium text-gray-600">Template Preview</div>
                            </div>
                        </div>
                        
                        <?php if ($current_template == $id): ?>
                            <div class="absolute top-2 right-2 bg-som-blue text-white px-2 py-1 rounded-full text-xs font-medium">
                                <i class="fas fa-check mr-1"></i>Active
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Template Info -->
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2"><?php echo $template['name']; ?></h3>
                        <p class="text-gray-600 text-sm mb-4"><?php echo $template['description']; ?></p>
                        
                        <!-- Color Scheme -->
                        <div class="flex items-center mb-4">
                            <span class="text-sm text-gray-500 mr-2">Colors:</span>
                            <div class="flex space-x-2">
                                <div class="w-4 h-4 rounded-full border border-gray-300" 
                                     style="background-color: <?php echo $template['primary_color']; ?>"></div>
                                <div class="w-4 h-4 rounded-full border border-gray-300" 
                                     style="background-color: <?php echo $template['secondary_color']; ?>"></div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="flex space-x-3">
                            <?php if ($current_template != $id): ?>
                                <form method="POST" class="flex-1">
                                    <input type="hidden" name="set_default_template" value="1">
                                    <input type="hidden" name="template_id" value="<?php echo $id; ?>">
                                    <button type="submit" class="w-full px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-md text-sm font-medium transition-colors">
                                        Set as Default
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="flex-1 px-4 py-2 bg-gray-100 text-gray-500 rounded-md text-sm font-medium text-center">
                                    Current Template
                                </div>
                            <?php endif; ?>
                            
                            <a href="generate-pdf.php?id=1&template=<?php echo $id; ?>" target="_blank" 
                               class="px-4 py-2 border border-gray-300 hover:bg-gray-50 text-gray-700 rounded-md text-sm font-medium transition-colors">
                                <i class="fas fa-eye mr-1"></i>Preview
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Custom Template Info -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="flex items-start">
                <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                <div>
                    <h3 class="text-lg font-medium text-blue-900 mb-2">Need a Custom Template?</h3>
                    <p class="text-blue-700 mb-3">
                        Want a completely custom invoice design that matches your brand perfectly? 
                        We can create a personalized template just for you.
                    </p>
                    <a href="mailto:<EMAIL>?subject=Custom Invoice Template Request" 
                       class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-envelope mr-2"></i>Request Custom Template
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
</body>
</html>
