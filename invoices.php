<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get search and pagination parameters
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? sanitizeInput($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitizeInput($_GET['date_to']) : '';
$amount_min = isset($_GET['amount_min']) ? (float)$_GET['amount_min'] : null;
$amount_max = isset($_GET['amount_max']) ? (float)$_GET['amount_max'] : null;
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 10;

// Build filters array
$filters = [
    'search' => $search,
    'status' => $status_filter,
    'date_from' => $date_from,
    'date_to' => $date_to,
    'amount_min' => $amount_min,
    'amount_max' => $amount_max
];

// Get invoices
$invoices = getInvoicesWithFilters($page, $limit, $filters);
$total_invoices = getTotalInvoiceCountWithFilters($filters);
$total_pages = ceil($total_invoices / $limit);

// Handle status updates
if ($_POST && isset($_POST['update_status'])) {
    $invoice_id = (int)$_POST['invoice_id'];
    $new_status = sanitizeInput($_POST['status']);
    
    if (updateInvoiceStatus($invoice_id, $new_status)) {
        $message = urlencode("Invoice status updated successfully!");
        header("Location: invoices.php?success=" . $message . ($search ? "&search=" . urlencode($search) : ""));
    } else {
        $message = urlencode("Failed to update invoice status.");
        header("Location: invoices.php?error=" . $message . ($search ? "&search=" . urlencode($search) : ""));
    }
    exit;
}

// Handle invoice deletion
if ($_POST && isset($_POST['delete_invoice'])) {
    $invoice_id = (int)$_POST['invoice_id'];
    
    if (deleteInvoice($invoice_id)) {
        $message = urlencode("Invoice deleted successfully!");
        header("Location: invoices.php?success=" . $message . ($search ? "&search=" . urlencode($search) : ""));
    } else {
        $message = urlencode("Failed to delete invoice.");
        header("Location: invoices.php?error=" . $message . ($search ? "&search=" . urlencode($search) : ""));
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Invoices - Som Milk Invoice Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">All Invoices</h1>
                    <p class="text-gray-600">Manage and track all your invoices</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="create-invoice.php" class="inline-flex items-center px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Create New Invoice
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_GET['success'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($_GET['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($_GET['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Advanced Search & Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900">Search & Filter</h2>
                <button id="toggle-filters" class="text-som-blue hover:text-som-light-blue text-sm font-medium">
                    <i class="fas fa-filter mr-1"></i>Advanced Filters
                </button>
            </div>

            <form method="GET" class="space-y-4">
                <!-- Basic Search -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                        <input type="text" name="search" value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue"
                               placeholder="Invoice number, customer name...">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                            <option value="">All Statuses</option>
                            <option value="draft" <?php echo ($_GET['status'] ?? '') == 'draft' ? 'selected' : ''; ?>>Draft</option>
                            <option value="pending" <?php echo ($_GET['status'] ?? '') == 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="paid" <?php echo ($_GET['status'] ?? '') == 'paid' ? 'selected' : ''; ?>>Paid</option>
                            <option value="overdue" <?php echo ($_GET['status'] ?? '') == 'overdue' ? 'selected' : ''; ?>>Overdue</option>
                            <option value="cancelled" <?php echo ($_GET['status'] ?? '') == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="w-full px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-md transition-colors">
                            <i class="fas fa-search mr-2"></i>Search
                        </button>
                    </div>
                </div>

                <!-- Advanced Filters (Hidden by default) -->
                <div id="advanced-filters" class="hidden border-t pt-4 mt-4">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                            <input type="date" name="date_from" value="<?php echo htmlspecialchars($_GET['date_from'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                            <input type="date" name="date_to" value="<?php echo htmlspecialchars($_GET['date_to'] ?? ''); ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Amount Min</label>
                            <input type="number" name="amount_min" value="<?php echo htmlspecialchars($_GET['amount_min'] ?? ''); ?>" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue"
                                   placeholder="0.00">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Amount Max</label>
                            <input type="number" name="amount_max" value="<?php echo htmlspecialchars($_GET['amount_max'] ?? ''); ?>" step="0.01"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue"
                                   placeholder="0.00">
                        </div>
                    </div>

                    <div class="flex justify-between items-center mt-4">
                        <a href="invoices.php" class="text-gray-600 hover:text-gray-800 text-sm">
                            <i class="fas fa-times mr-1"></i>Clear All Filters
                        </a>

                        <div class="flex space-x-2">
                            <button type="button" onclick="exportInvoices()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md text-sm">
                                <i class="fas fa-download mr-1"></i>Export CSV
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <form method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Search by invoice number or customer name..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                </div>
                <div class="flex gap-2">
                    <button type="submit" class="px-6 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                    <?php if ($search): ?>
                        <a href="invoices.php" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition duration-200">
                            <i class="fas fa-times mr-2"></i>Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Invoices Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <?php if (empty($invoices)): ?>
                <div class="p-8 text-center">
                    <i class="fas fa-file-invoice text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
                    <p class="text-gray-600 mb-4">
                        <?php echo $search ? 'No invoices match your search criteria.' : 'You haven\'t created any invoices yet.'; ?>
                    </p>
                    <a href="create-invoice.php" class="inline-flex items-center px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Create Your First Invoice
                    </a>
                </div>
            <?php else: ?>
                <!-- Desktop Table -->
                <div class="hidden md:block overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($invoices as $invoice): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-gray-900"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                                        <?php if ($invoice['customer_email']): ?>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($invoice['customer_email']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('M d, Y', strtotime($invoice['invoice_date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $invoice['due_date'] ? date('M d, Y', strtotime($invoice['due_date'])) : '-'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">$<?php echo number_format($invoice['total_amount'], 2); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center space-x-2">
                                            <?php
                                            $status_colors = [
                                                'draft' => 'bg-gray-100 text-gray-800',
                                                'pending' => 'bg-yellow-100 text-yellow-800',
                                                'paid' => 'bg-green-100 text-green-800',
                                                'overdue' => 'bg-red-100 text-red-800',
                                                'cancelled' => 'bg-red-100 text-red-800'
                                            ];
                                            $status_class = $status_colors[$invoice['status']] ?? 'bg-gray-100 text-gray-800';
                                            ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                                <?php echo ucfirst($invoice['status']); ?>
                                            </span>

                                            <!-- Status Update Dropdown -->
                                            <?php if ($invoice['status'] !== 'paid'): ?>
                                                <div class="relative inline-block text-left">
                                                    <button type="button" class="status-dropdown-btn text-gray-400 hover:text-gray-600"
                                                            data-invoice-id="<?php echo $invoice['id']; ?>"
                                                            data-current-status="<?php echo $invoice['status']; ?>">
                                                        <i class="fas fa-chevron-down text-xs"></i>
                                                    </button>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2">
                                            <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-som-blue hover:text-som-light-blue" title="View Invoice">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-green-600 hover:text-green-800" title="Edit Invoice">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="generate-pdf.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-purple-600 hover:text-purple-800" title="Download PDF">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button onclick="deleteInvoice(<?php echo $invoice['id']; ?>)" 
                                                    class="text-red-600 hover:text-red-800" title="Delete Invoice">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Cards -->
                <div class="md:hidden">
                    <?php foreach ($invoices as $invoice): ?>
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <div class="font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                                    <div class="text-sm text-gray-600"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-900">$<?php echo number_format($invoice['total_amount'], 2); ?></div>
                                    <?php
                                    $status_colors = [
                                        'draft' => 'bg-gray-100 text-gray-800',
                                        'pending' => 'bg-yellow-100 text-yellow-800',
                                        'paid' => 'bg-green-100 text-green-800',
                                        'overdue' => 'bg-red-100 text-red-800',
                                        'cancelled' => 'bg-red-100 text-red-800'
                                    ];
                                    $status_class = $status_colors[$invoice['status']] ?? 'bg-gray-100 text-gray-800';
                                    ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                        <?php echo ucfirst($invoice['status']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-sm text-gray-500 mb-3">
                                <span><?php echo date('M d, Y', strtotime($invoice['invoice_date'])); ?></span>
                                <span>Due: <?php echo $invoice['due_date'] ? date('M d, Y', strtotime($invoice['due_date'])) : '-'; ?></span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-3">
                                    <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                       class="text-som-blue hover:text-som-light-blue">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                       class="text-green-600 hover:text-green-800">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="generate-pdf.php?id=<?php echo $invoice['id']; ?>" 
                                       class="text-purple-600 hover:text-purple-800">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button onclick="deleteInvoice(<?php echo $invoice['id']; ?>)" 
                                            class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="mt-6 flex justify-center">
                <nav class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="px-3 py-2 <?php echo $i == $page ? 'bg-som-blue text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'; ?> rounded-md">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Invoice</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to delete this invoice? This action cannot be undone.</p>
            <div class="flex justify-end space-x-3">
                <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md">
                    Cancel
                </button>
                <form id="delete-form" method="POST" class="inline">
                    <input type="hidden" name="delete_invoice" value="1">
                    <input type="hidden" name="invoice_id" id="delete-invoice-id">
                    <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div id="status-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Update Invoice Status</h3>
                    <form id="status-form" method="POST" action="update-status.php">
                        <input type="hidden" name="invoice_id" id="status-invoice-id">
                        <input type="hidden" name="redirect" value="invoices.php">

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                            <select name="status" id="status-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue">
                                <!-- Options will be populated by JavaScript -->
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                            <textarea name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue" placeholder="Add a note about this status change..."></textarea>
                        </div>

                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeStatusModal()" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md">
                                Cancel
                            </button>
                            <button type="submit" class="px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-md">
                                Update Status
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
    <script>
        // Status workflow definitions
        const statusTransitions = {
            'draft': ['pending', 'cancelled'],
            'pending': ['paid', 'overdue', 'cancelled'],
            'overdue': ['paid', 'cancelled'],
            'paid': [],
            'cancelled': ['draft']
        };

        // Status update functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers to status dropdown buttons
            document.querySelectorAll('.status-dropdown-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const invoiceId = this.dataset.invoiceId;
                    const currentStatus = this.dataset.currentStatus;
                    showStatusModal(invoiceId, currentStatus);
                });
            });
        });

        function showStatusModal(invoiceId, currentStatus) {
            document.getElementById('status-invoice-id').value = invoiceId;

            // Populate status options based on current status
            const statusSelect = document.getElementById('status-select');
            statusSelect.innerHTML = '';

            const availableStatuses = statusTransitions[currentStatus] || [];
            availableStatuses.forEach(status => {
                const option = document.createElement('option');
                option.value = status;
                option.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusSelect.appendChild(option);
            });

            document.getElementById('status-modal').classList.remove('hidden');
        }

        function closeStatusModal() {
            document.getElementById('status-modal').classList.add('hidden');
        }

        // Advanced filters toggle
        document.getElementById('toggle-filters').addEventListener('click', function() {
            const advancedFilters = document.getElementById('advanced-filters');
            const isHidden = advancedFilters.classList.contains('hidden');

            if (isHidden) {
                advancedFilters.classList.remove('hidden');
                this.innerHTML = '<i class="fas fa-filter mr-1"></i>Hide Filters';
            } else {
                advancedFilters.classList.add('hidden');
                this.innerHTML = '<i class="fas fa-filter mr-1"></i>Advanced Filters';
            }
        });

        // Export invoices function
        function exportInvoices() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            window.location.href = 'export-invoices.php?' + params.toString();
        }

        // Show advanced filters if any advanced filter is active
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const hasAdvancedFilters = urlParams.get('date_from') || urlParams.get('date_to') ||
                                     urlParams.get('amount_min') || urlParams.get('amount_max');

            if (hasAdvancedFilters) {
                document.getElementById('advanced-filters').classList.remove('hidden');
                document.getElementById('toggle-filters').innerHTML = '<i class="fas fa-filter mr-1"></i>Hide Filters';
            }
        });

        function deleteInvoice(invoiceId) {
            document.getElementById('delete-invoice-id').value = invoiceId;
            document.getElementById('delete-modal').classList.remove('hidden');
            document.getElementById('delete-modal').classList.add('flex');
        }

        function closeDeleteModal() {
            document.getElementById('delete-modal').classList.add('hidden');
            document.getElementById('delete-modal').classList.remove('flex');
        }
    </script>
</body>
</html>
