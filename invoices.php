<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get search and pagination parameters
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = 10;

// Get invoices
$invoices = getInvoices($page, $limit, $search);
$total_invoices = getTotalInvoiceCount($search);
$total_pages = ceil($total_invoices / $limit);

// Handle status updates
if ($_POST && isset($_POST['update_status'])) {
    $invoice_id = (int)$_POST['invoice_id'];
    $new_status = sanitizeInput($_POST['status']);
    
    if (updateInvoiceStatus($invoice_id, $new_status)) {
        $_SESSION['success_message'] = "Invoice status updated successfully!";
    } else {
        $_SESSION['error_message'] = "Failed to update invoice status.";
    }
    
    header("Location: invoices.php" . ($search ? "?search=" . urlencode($search) : ""));
    exit;
}

// Handle invoice deletion
if ($_POST && isset($_POST['delete_invoice'])) {
    $invoice_id = (int)$_POST['invoice_id'];
    
    if (deleteInvoice($invoice_id)) {
        $_SESSION['success_message'] = "Invoice deleted successfully!";
    } else {
        $_SESSION['error_message'] = "Failed to delete invoice.";
    }
    
    header("Location: invoices.php" . ($search ? "?search=" . urlencode($search) : ""));
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Invoices - Som Milk Invoice Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#1e40af',
                        'som-light-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">All Invoices</h1>
                    <p class="text-gray-600">Manage and track all your invoices</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="create-invoice.php" class="inline-flex items-center px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>
                        Create New Invoice
                    </a>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-check-circle mr-2"></i>
                <?php echo htmlspecialchars($_SESSION['success_message']); unset($_SESSION['success_message']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($_SESSION['error_message']); unset($_SESSION['error_message']); ?>
            </div>
        <?php endif; ?>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <form method="GET" class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                           placeholder="Search by invoice number or customer name..."
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent">
                </div>
                <div class="flex gap-2">
                    <button type="submit" class="px-6 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                    <?php if ($search): ?>
                        <a href="invoices.php" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition duration-200">
                            <i class="fas fa-times mr-2"></i>Clear
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Invoices Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <?php if (empty($invoices)): ?>
                <div class="p-8 text-center">
                    <i class="fas fa-file-invoice text-4xl text-gray-300 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
                    <p class="text-gray-600 mb-4">
                        <?php echo $search ? 'No invoices match your search criteria.' : 'You haven\'t created any invoices yet.'; ?>
                    </p>
                    <a href="create-invoice.php" class="inline-flex items-center px-4 py-2 bg-som-blue hover:bg-som-light-blue text-white rounded-lg transition duration-200">
                        <i class="fas fa-plus mr-2"></i>Create Your First Invoice
                    </a>
                </div>
            <?php else: ?>
                <!-- Desktop Table -->
                <div class="hidden md:block overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($invoices as $invoice): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-gray-900"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                                        <?php if ($invoice['customer_email']): ?>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($invoice['customer_email']); ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('M d, Y', strtotime($invoice['invoice_date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $invoice['due_date'] ? date('M d, Y', strtotime($invoice['due_date'])) : '-'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">$<?php echo number_format($invoice['total_amount'], 2); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_colors = [
                                            'draft' => 'bg-gray-100 text-gray-800',
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'paid' => 'bg-green-100 text-green-800',
                                            'overdue' => 'bg-red-100 text-red-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];
                                        $status_class = $status_colors[$invoice['status']] ?? 'bg-gray-100 text-gray-800';
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                            <?php echo ucfirst($invoice['status']); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2">
                                            <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-som-blue hover:text-som-light-blue" title="View Invoice">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-green-600 hover:text-green-800" title="Edit Invoice">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="generate-pdf.php?id=<?php echo $invoice['id']; ?>" 
                                               class="text-purple-600 hover:text-purple-800" title="Download PDF">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button onclick="deleteInvoice(<?php echo $invoice['id']; ?>)" 
                                                    class="text-red-600 hover:text-red-800" title="Delete Invoice">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Cards -->
                <div class="md:hidden">
                    <?php foreach ($invoices as $invoice): ?>
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <div class="font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                                    <div class="text-sm text-gray-600"><?php echo htmlspecialchars($invoice['customer_name']); ?></div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-900">$<?php echo number_format($invoice['total_amount'], 2); ?></div>
                                    <?php
                                    $status_colors = [
                                        'draft' => 'bg-gray-100 text-gray-800',
                                        'pending' => 'bg-yellow-100 text-yellow-800',
                                        'paid' => 'bg-green-100 text-green-800',
                                        'overdue' => 'bg-red-100 text-red-800',
                                        'cancelled' => 'bg-red-100 text-red-800'
                                    ];
                                    $status_class = $status_colors[$invoice['status']] ?? 'bg-gray-100 text-gray-800';
                                    ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                        <?php echo ucfirst($invoice['status']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-sm text-gray-500 mb-3">
                                <span><?php echo date('M d, Y', strtotime($invoice['invoice_date'])); ?></span>
                                <span>Due: <?php echo $invoice['due_date'] ? date('M d, Y', strtotime($invoice['due_date'])) : '-'; ?></span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-3">
                                    <a href="view-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                       class="text-som-blue hover:text-som-light-blue">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit-invoice.php?id=<?php echo $invoice['id']; ?>" 
                                       class="text-green-600 hover:text-green-800">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="generate-pdf.php?id=<?php echo $invoice['id']; ?>" 
                                       class="text-purple-600 hover:text-purple-800">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button onclick="deleteInvoice(<?php echo $invoice['id']; ?>)" 
                                            class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="mt-6 flex justify-center">
                <nav class="flex items-center space-x-2">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="px-3 py-2 <?php echo $i == $page ? 'bg-som-blue text-white' : 'bg-white border border-gray-300 hover:bg-gray-50'; ?> rounded-md">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>" 
                           class="px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </nav>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-sm mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Invoice</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to delete this invoice? This action cannot be undone.</p>
            <div class="flex justify-end space-x-3">
                <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md">
                    Cancel
                </button>
                <form id="delete-form" method="POST" class="inline">
                    <input type="hidden" name="delete_invoice" value="1">
                    <input type="hidden" name="invoice_id" id="delete-invoice-id">
                    <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/main.js"></script>
    <script>
        function deleteInvoice(invoiceId) {
            document.getElementById('delete-invoice-id').value = invoiceId;
            document.getElementById('delete-modal').classList.remove('hidden');
            document.getElementById('delete-modal').classList.add('flex');
        }
        
        function closeDeleteModal() {
            document.getElementById('delete-modal').classList.add('hidden');
            document.getElementById('delete-modal').classList.remove('flex');
        }
    </script>
</body>
</html>
