<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if export is requested
if (!isset($_GET['export']) || $_GET['export'] !== 'csv') {
    header("Location: invoices.php");
    exit;
}

// Get the same filters used in the invoices page
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitizeInput($_GET['status']) : '';
$date_from = isset($_GET['date_from']) ? sanitizeInput($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitizeInput($_GET['date_to']) : '';
$amount_min = isset($_GET['amount_min']) ? (float)$_GET['amount_min'] : null;
$amount_max = isset($_GET['amount_max']) ? (float)$_GET['amount_max'] : null;

// Build filters array
$filters = [
    'search' => $search,
    'status' => $status_filter,
    'date_from' => $date_from,
    'date_to' => $date_to,
    'amount_min' => $amount_min,
    'amount_max' => $amount_max
];

// Get all invoices matching the filters (no pagination for export)
$invoices = getInvoicesWithFilters(1, 10000, $filters); // Large limit to get all results

// Set headers for CSV download
$filename = 'invoices_export_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Create file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Add CSV headers
$headers = [
    'Invoice Number',
    'Customer Name',
    'Customer Email',
    'Customer Phone',
    'Invoice Date',
    'Due Date',
    'Status',
    'Subtotal',
    'Tax Amount',
    'Total Amount',
    'Notes',
    'Created At',
    'Updated At'
];

fputcsv($output, $headers);

// Add invoice data
foreach ($invoices as $invoice) {
    $row = [
        $invoice['invoice_number'],
        $invoice['customer_name'] ?: $invoice['customer_email'],
        $invoice['customer_email'],
        $invoice['customer_phone'] ?: '',
        $invoice['invoice_date'],
        $invoice['due_date'] ?: '',
        ucfirst($invoice['status']),
        number_format($invoice['subtotal'], 2),
        number_format($invoice['tax_amount'], 2),
        number_format($invoice['total_amount'], 2),
        $invoice['notes'] ?: '',
        $invoice['created_at'],
        $invoice['updated_at']
    ];
    
    fputcsv($output, $row);
}

// Close the file pointer
fclose($output);
exit;
?>
