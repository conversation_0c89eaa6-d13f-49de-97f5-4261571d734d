<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Require admin or manager login
requireLogin();
if (!hasRole('manager')) {
    header("Location: index.php?error=access_denied");
    exit();
}

$page_title = "Users Management";
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['create_user'])) {
        $userData = [
            'username' => trim($_POST['username']),
            'email' => trim($_POST['email']),
            'password' => $_POST['password'],
            'first_name' => trim($_POST['first_name']),
            'last_name' => trim($_POST['last_name']),
            'user_type' => $_POST['user_type'],
            'status' => $_POST['status'] ?? 'pending',
            'phone' => trim($_POST['phone'] ?? ''),
            'company_name' => trim($_POST['company_name'] ?? ''),
            'job_title' => trim($_POST['job_title'] ?? ''),
            'customer_portal_access' => isset($_POST['customer_portal_access']),
            'email_verified' => isset($_POST['email_verified'])
        ];
        
        $result = createUser($userData);
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
    
    if (isset($_POST['update_user_status'])) {
        $userId = (int)$_POST['user_id'];
        $newStatus = $_POST['new_status'];
        
        $result = updateUser($userId, ['status' => $newStatus]);
        if ($result['success']) {
            $success_message = "User status updated successfully!";
        } else {
            $error_message = $result['message'];
        }
    }
}

// Get filters
$filters = [];
if (!empty($_GET['user_type'])) {
    $filters['user_type'] = $_GET['user_type'];
}
if (!empty($_GET['status'])) {
    $filters['status'] = $_GET['status'];
}
if (!empty($_GET['search'])) {
    $filters['search'] = $_GET['search'];
}

// Get users and statistics
$users = getUsers($filters, 100, 0);
$userStats = getUserStats();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Som Milk</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#2563EB',
                        'som-light-blue': '#3b82f6',
                        'som-green': '#059669',
                        'som-red': '#DC2626'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Users Management</h1>
            <p class="text-gray-600">Manage system users, customers, staff, and contractors</p>
        </div>

        <!-- Messages -->
        <?php if (!empty($success_message)): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Users</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $userStats['total'] ?? 0; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-user-check text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Users</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $userStats['by_status']['active'] ?? 0; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-store text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Customer Portal</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $userStats['customer_portal_users'] ?? 0; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="fas fa-user-plus text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Recent (30d)</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $userStats['recent_registrations'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Create User Form -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">
                        <i class="fas fa-user-plus mr-2 text-som-blue"></i>Create New User
                    </h2>
                    
                    <form method="POST" class="space-y-4">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                                <input type="text" name="first_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                                <input type="text" name="last_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                            <input type="text" name="username" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input type="password" name="password" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                            <input type="tel" name="phone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">User Type</label>
                            <select name="user_type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                                <option value="customer">Customer</option>
                                <option value="staff">Staff</option>
                                <option value="contractor">Contractor</option>
                                <option value="vendor">Vendor</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                                <option value="pending">Pending</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                            <input type="text" name="company_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
                            <input type="text" name="job_title" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-som-blue focus:border-som-blue text-sm">
                        </div>
                        
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="customer_portal_access" class="rounded border-gray-300 text-som-blue focus:ring-som-blue">
                                <span class="ml-2 text-sm text-gray-700">Customer Portal Access</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="email_verified" class="rounded border-gray-300 text-som-blue focus:ring-som-blue">
                                <span class="ml-2 text-sm text-gray-700">Email Verified</span>
                            </label>
                        </div>
                        
                        <button type="submit" name="create_user" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-2 px-4 rounded-lg transition duration-200 text-sm">
                            <i class="fas fa-plus mr-2"></i>Create User
                        </button>
                    </form>
                </div>
            </div>

            <!-- Users List -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <h2 class="text-xl font-semibold text-gray-900 mb-4 sm:mb-0">
                                <i class="fas fa-users mr-2 text-som-blue"></i>System Users
                            </h2>
                            
                            <!-- Filters -->
                            <div class="flex flex-col sm:flex-row gap-2">
                                <form method="GET" class="flex gap-2">
                                    <select name="user_type" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                        <option value="">All Types</option>
                                        <option value="customer" <?php echo ($_GET['user_type'] ?? '') == 'customer' ? 'selected' : ''; ?>>Customer</option>
                                        <option value="staff" <?php echo ($_GET['user_type'] ?? '') == 'staff' ? 'selected' : ''; ?>>Staff</option>
                                        <option value="contractor" <?php echo ($_GET['user_type'] ?? '') == 'contractor' ? 'selected' : ''; ?>>Contractor</option>
                                        <option value="vendor" <?php echo ($_GET['user_type'] ?? '') == 'vendor' ? 'selected' : ''; ?>>Vendor</option>
                                    </select>
                                    
                                    <select name="status" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                        <option value="">All Status</option>
                                        <option value="active" <?php echo ($_GET['status'] ?? '') == 'active' ? 'selected' : ''; ?>>Active</option>
                                        <option value="pending" <?php echo ($_GET['status'] ?? '') == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="inactive" <?php echo ($_GET['status'] ?? '') == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                        <option value="suspended" <?php echo ($_GET['status'] ?? '') == 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                    </select>
                                    
                                    <input type="text" name="search" placeholder="Search users..." value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <button type="submit" class="bg-som-blue text-white px-4 py-2 rounded-lg text-sm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Portal</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (!empty($users)): ?>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['full_name']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($user['username']); ?> • <?php echo htmlspecialchars($user['email']); ?></div>
                                                    <?php if (!empty($user['phone'])): ?>
                                                        <div class="text-xs text-gray-400"><?php echo htmlspecialchars($user['phone']); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    <?php 
                                                    $typeColors = [
                                                        'customer' => 'bg-blue-100 text-blue-800',
                                                        'staff' => 'bg-green-100 text-green-800',
                                                        'contractor' => 'bg-yellow-100 text-yellow-800',
                                                        'vendor' => 'bg-purple-100 text-purple-800'
                                                    ];
                                                    echo $typeColors[$user['user_type']] ?? 'bg-gray-100 text-gray-800';
                                                    ?>">
                                                    <?php echo ucfirst($user['user_type']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    <?php 
                                                    $statusColors = [
                                                        'active' => 'bg-green-100 text-green-800',
                                                        'pending' => 'bg-yellow-100 text-yellow-800',
                                                        'inactive' => 'bg-gray-100 text-gray-800',
                                                        'suspended' => 'bg-red-100 text-red-800'
                                                    ];
                                                    echo $statusColors[$user['status']] ?? 'bg-gray-100 text-gray-800';
                                                    ?>">
                                                    <?php echo ucfirst($user['status']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php if (!empty($user['company_name'])): ?>
                                                    <div><?php echo htmlspecialchars($user['company_name']); ?></div>
                                                    <?php if (!empty($user['job_title'])): ?>
                                                        <div class="text-xs text-gray-400"><?php echo htmlspecialchars($user['job_title']); ?></div>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-gray-400">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php if ($user['customer_portal_access']): ?>
                                                    <span class="text-green-600"><i class="fas fa-check-circle"></i> Yes</span>
                                                <?php else: ?>
                                                    <span class="text-gray-400"><i class="fas fa-times-circle"></i> No</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <select name="new_status" onchange="this.form.submit()" class="text-xs border border-gray-300 rounded px-2 py-1">
                                                        <option value="">Change Status</option>
                                                        <option value="active" <?php echo $user['status'] == 'active' ? 'disabled' : ''; ?>>Active</option>
                                                        <option value="pending" <?php echo $user['status'] == 'pending' ? 'disabled' : ''; ?>>Pending</option>
                                                        <option value="inactive" <?php echo $user['status'] == 'inactive' ? 'disabled' : ''; ?>>Inactive</option>
                                                        <option value="suspended" <?php echo $user['status'] == 'suspended' ? 'disabled' : ''; ?>>Suspended</option>
                                                    </select>
                                                    <input type="hidden" name="update_user_status" value="1">
                                                </form>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">No users found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Dashboard -->
        <div class="mt-8 text-center">
            <a href="index.php" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
