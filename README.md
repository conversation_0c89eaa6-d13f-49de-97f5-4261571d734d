# Som Milk Invoice Management System

A modern, responsive invoice management system built with PHP, PostgreSQL, and Tailwind CSS. This system allows <PERSON><PERSON> Milk to create, manage, and track invoices with professional PDF generation capabilities. Now optimized for Replit deployment with PostgreSQL database support.

## Features

### ✅ Completed Features

- **Modern Dashboard**: Clean, responsive dashboard with invoice statistics and recent invoices
- **Invoice Creation**: Comprehensive form with real-time calculations and auto-generation
- **Invoice Management**: Full CRUD operations with search and filtering
- **PDF Generation**: Professional PDF invoices with company branding
- **Customer Management**: Basic customer information storage and management
- **Responsive Design**: Mobile-first design using Tailwind CSS
- **Settings Management**: Company information and system configuration
- **Database Integration**: Secure MySQL database with proper relationships

### 🚀 Key Capabilities

- Auto-generation of invoice numbers with customizable prefix (SM-)
- Real-time calculation of totals, taxes, and subtotals
- Professional PDF invoice generation
- Invoice status tracking (Draft, Pending, Paid, Overdue, Cancelled)
- Search and filter invoices by number or customer name
- Responsive design for desktop, tablet, and mobile devices
- Secure database operations with prepared statements

## Technology Stack

- **Backend**: PHP 8.2+ with PDO for database operations
- **Database**: PostgreSQL 13+ with proper foreign key relationships and triggers
- **Frontend**: HTML5, JavaScript (Vanilla), Tailwind CSS
- **PDF Generation**: HTML-to-PDF conversion with print-friendly styling
- **Icons**: Font Awesome 6.0
- **Server**: Built-in PHP server (optimized for Replit deployment)
- **Deployment**: Replit-ready with Nix configuration

## Installation

### Prerequisites

- PHP 8.2 or higher
- PostgreSQL 13 or higher
- Web browser with JavaScript enabled

### Setup Instructions

#### For Replit Deployment (Recommended)

1. **Fork/Import to Replit**

   - Import this repository to Replit
   - Replit will automatically detect the configuration

2. **Run Setup Script**

   ```bash
   chmod +x setup_replit.sh
   ./setup_replit.sh
   ```

3. **Complete Installation**

   - Open your Replit app URL
   - Navigate to `/install_postgresql.php`
   - Follow the installation wizard
   - Use these default settings:
     - Host: `localhost`
     - Port: `5432`
     - Database: `sommilk_invoice`
     - Username: Your Replit username
     - Password: (leave empty)

4. **Access the System**
   - After installation, visit your Replit app URL
   - Start creating invoices and managing your business!

#### For Local Development

1. **Install PostgreSQL**

   - Install PostgreSQL on your system
   - Create a database named `sommilk_invoice`

2. **Configure Environment**

   ```bash
   export DB_HOST=localhost
   export DB_PORT=5432
   export DB_NAME=sommilk_invoice
   export DB_USER=postgres
   export DB_PASSWORD=your_password
   ```

3. **Run Installation**

   - Start PHP built-in server: `php -S localhost:8000`
   - Navigate to: `http://localhost:8000/install_postgresql.php`
   - Follow the installation wizard

4. **Access the System**
   - Visit: `http://localhost:8000/`
   - Start using the system!

## File Structure

```
sommilk_invoice/
├── assets/
│   ├── css/
│   ├── js/
│   │   └── main.js          # JavaScript for form interactions
│   └── images/
├── database/
│   ├── schema.sql           # Original MySQL schema
│   └── postgresql_schema.sql # PostgreSQL schema for Replit
├── includes/
│   ├── config.php           # Database configuration
│   ├── functions.php        # Core business logic functions
│   ├── navbar.php           # Navigation component
│   └── footer.php           # Footer component
├── index.php                # Dashboard homepage
├── create-invoice.php       # Invoice creation form
├── process-invoice.php      # Invoice form processing
├── invoices.php             # Invoice listing and management
├── view-invoice.php         # Individual invoice view
├── generate-pdf.php         # PDF generation
├── customers.php            # Customer management (placeholder)
├── settings.php             # System settings
├── install.php              # Installation wizard
└── README.md               # This file
```

## Usage Guide

### Creating an Invoice

1. Navigate to "Create Invoice" from the dashboard
2. Fill in customer information
3. Add invoice items with descriptions, quantities, and prices
4. Set tax rate if applicable
5. Add notes if needed
6. Choose to save as draft or mark as pending
7. Click "Create Invoice" to save

### Managing Invoices

1. Go to "All Invoices" to view all invoices
2. Use the search bar to find specific invoices
3. Click actions to view, edit, download PDF, or delete
4. Update invoice status as needed

### PDF Generation

- Click "View PDF" to see a print-friendly version
- Click "Download PDF" to save the invoice as PDF
- PDFs include company branding and professional formatting

### System Settings

1. Go to "Settings" to configure:
   - Company information and contact details
   - Banking information for payments
   - Default tax rates and invoice settings
   - Invoice numbering prefix

## Database Schema

### Main Tables

- **company_info**: Company details and settings
- **customers**: Customer information and contacts
- **invoices**: Invoice headers with totals and status
- **invoice_items**: Individual line items for each invoice
- **payments**: Payment tracking (future enhancement)
- **system_settings**: Application configuration

### Key Relationships

- Invoices → Customers (Many-to-One)
- Invoice Items → Invoices (Many-to-One)
- Payments → Invoices (Many-to-One)

## Security Features

- **SQL Injection Protection**: All database queries use prepared statements
- **Input Sanitization**: All user inputs are properly sanitized
- **Session Management**: Secure session handling for user state
- **Error Handling**: Comprehensive error logging and user-friendly messages

## Customization

### Styling

- Modify Tailwind CSS classes in HTML files
- Add custom CSS in `assets/css/` directory
- Update color scheme by modifying the `som-blue` theme colors

### Business Logic

- Core functions are in `includes/functions.php`
- Database operations use PDO for security and flexibility
- Add new features by extending existing functions

### PDF Templates

- Modify `generate-pdf.php` for custom PDF layouts
- Update company branding and styling
- Add additional fields or sections as needed

## Future Enhancements

### Planned Features

- Advanced customer management with history
- Payment tracking and reconciliation
- Recurring invoice automation
- Email invoice delivery
- Advanced reporting and analytics
- Multi-user support with roles
- API endpoints for integrations
- Backup and restore functionality

### Integration Opportunities

- Payment gateways (Stripe, PayPal)
- Email services (SendGrid, Mailgun)
- Accounting software (QuickBooks, Xero)
- CRM systems
- Cloud storage (Google Drive, Dropbox)

## Support and Maintenance

### Regular Maintenance

- Keep PHP and MySQL updated
- Regular database backups
- Monitor error logs
- Update dependencies as needed

### Troubleshooting

- Check Apache/MySQL services are running
- Verify database connection settings
- Review error logs in `includes/` directory
- Ensure proper file permissions

## License

This project is developed for Som Milk's internal use. All rights reserved.

## Contact

For technical support or feature requests, please contact the development team.

---

**Som Milk Invoice Management System v1.0**  
_Professional invoice management made simple_
