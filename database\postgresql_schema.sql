-- Som Milk Invoice Management System Database Schema
-- PostgreSQL Version for Replit Deployment
-- Created: 2025-07-07

-- Company Information Table
CREATE TABLE IF NOT EXISTS company_info (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL DEFAULT 'Som Milk',
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    phone VARCHAR(50),
    email VARCHAR(255),
    website VARCHAR(255),
    logo_path VARCHAR(500),
    
    -- Banking Information
    bank_name VARCHAR(255),
    account_number VARCHAR(100),
    routing_number VARCHAR(50),
    
    -- Tax Information
    tax_id VARCHAR(50),
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customers Table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    company_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'United States',
    
    -- Additional fields
    contact_person VARCHAR(255),
    notes TEXT,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoices Table
CREATE TABLE IF NOT EXISTS invoices (
    id SERIAL PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INTEGER REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Customer snapshot (in case customer is deleted)
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_address TEXT,
    customer_city VARCHAR(100),
    customer_state VARCHAR(50),
    customer_zip VARCHAR(20),
    
    -- Invoice details
    invoice_date DATE NOT NULL,
    due_date DATE,
    
    -- Financial information
    subtotal DECIMAL(10,2) DEFAULT 0.00,
    tax_rate DECIMAL(5,2) DEFAULT 0.00,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    
    -- Status and notes
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'pending', 'paid', 'overdue', 'cancelled')),
    notes TEXT,
    terms TEXT,
    
    -- Payment information
    payment_method VARCHAR(50),
    payment_date DATE,
    payment_reference VARCHAR(100),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoice Items Table
CREATE TABLE IF NOT EXISTS invoice_items (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER REFERENCES invoices(id) ON DELETE CASCADE,
    
    -- Item details
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1.00,
    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    
    -- Additional fields
    item_order INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- System Settings Table
CREATE TABLE IF NOT EXISTS system_settings (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string' CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_invoices_customer_id ON invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(invoice_date);
CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);

-- Insert default company information
INSERT INTO company_info (
    company_name, address, city, state, zip_code, country,
    phone, email, website,
    bank_name, account_number, routing_number,
    tax_rate
) VALUES (
    'Som Milk',
    '123 Dairy Street',
    'Milk City',
    'MC',
    '12345',
    'United States',
    '+****************',
    '<EMAIL>',
    'www.sommilk.com',
    'First National Bank',
    '*********0',
    '*********',
    0.00
) ON CONFLICT DO NOTHING;

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('invoice_prefix', 'SM-', 'string', 'Prefix for invoice numbers'),
('next_invoice_number', '1001', 'number', 'Next invoice number to use'),
('default_tax_rate', '0.00', 'number', 'Default tax rate percentage'),
('invoice_due_days', '30', 'number', 'Default number of days for invoice due date'),
('currency_symbol', '$', 'string', 'Currency symbol to display'),
('date_format', 'Y-m-d', 'string', 'Date format for display'),
('items_per_page', '10', 'number', 'Number of items to show per page'),
('company_logo_path', '', 'string', 'Path to company logo file')
ON CONFLICT (setting_key) DO NOTHING;

-- Insert sample customer data
INSERT INTO customers (
    customer_name, company_name, email, phone, address, city, state, zip_code
) VALUES
('John Smith', 'ABC Corporation', '<EMAIL>', '(*************', '456 Business Ave', 'Business City', 'BC', '54321'),
('Jane Doe', 'XYZ Industries', '<EMAIL>', '(*************', '789 Industrial Blvd', 'Industry Town', 'IT', '67890'),
('Mike Johnson', 'Johnson Enterprises', '<EMAIL>', '(*************', '321 Commerce St', 'Commerce City', 'CC', '09876')
ON CONFLICT DO NOTHING;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_company_info_updated_at BEFORE UPDATE ON company_info FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically calculate invoice totals
CREATE OR REPLACE FUNCTION calculate_invoice_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the invoice totals when items are added/updated/deleted
    UPDATE invoices 
    SET 
        subtotal = (
            SELECT COALESCE(SUM(total_price), 0) 
            FROM invoice_items 
            WHERE invoice_id = COALESCE(NEW.invoice_id, OLD.invoice_id)
        ),
        tax_amount = (
            SELECT COALESCE(SUM(total_price), 0) * (tax_rate / 100)
            FROM invoice_items 
            WHERE invoice_id = COALESCE(NEW.invoice_id, OLD.invoice_id)
        ),
        total_amount = (
            SELECT COALESCE(SUM(total_price), 0) * (1 + tax_rate / 100)
            FROM invoice_items 
            WHERE invoice_id = COALESCE(NEW.invoice_id, OLD.invoice_id)
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE id = COALESCE(NEW.invoice_id, OLD.invoice_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Create triggers for automatic invoice total calculation
CREATE TRIGGER calculate_totals_on_insert AFTER INSERT ON invoice_items FOR EACH ROW EXECUTE FUNCTION calculate_invoice_totals();
CREATE TRIGGER calculate_totals_on_update AFTER UPDATE ON invoice_items FOR EACH ROW EXECUTE FUNCTION calculate_invoice_totals();
CREATE TRIGGER calculate_totals_on_delete AFTER DELETE ON invoice_items FOR EACH ROW EXECUTE FUNCTION calculate_invoice_totals();
