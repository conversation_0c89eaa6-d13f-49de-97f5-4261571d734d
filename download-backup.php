<?php
session_start();
require_once 'includes/config.php';

// Check if file parameter is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    $_SESSION['error_message'] = "No backup file specified.";
    header("Location: backup-export.php");
    exit;
}

$filename = basename($_GET['file']); // Sanitize filename
$filepath = 'backups/' . $filename;

// Check if file exists and is a SQL file
if (!file_exists($filepath) || pathinfo($filename, PATHINFO_EXTENSION) !== 'sql') {
    $_SESSION['error_message'] = "Backup file not found or invalid.";
    header("Location: backup-export.php");
    exit;
}

// Set headers for file download
header('Content-Type: application/octet-stream');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Pragma: no-cache');
header('Expires: 0');

// Output file content
readfile($filepath);
exit;
?>
