<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

$page_title = "Customer Portal";

// Handle customer login
if ($_POST && isset($_POST['customer_login'])) {
    $email = sanitizeInput($_POST['email']);
    $invoice_number = sanitizeInput($_POST['invoice_number']);
    
    if (!empty($email) && !empty($invoice_number)) {
        // Find customer and invoice
        $stmt = $conn->prepare("
            SELECT i.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone, c.address as customer_address
            FROM invoices i 
            LEFT JOIN customers c ON i.customer_id = c.id 
            WHERE (i.customer_email = ? OR c.email = ?) AND i.invoice_number = ?
        ");
        $stmt->bind_param("sss", $email, $email, $invoice_number);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $invoice = $result->fetch_assoc();
            $_SESSION['customer_portal'] = [
                'email' => $email,
                'invoice_id' => $invoice['id'],
                'customer_name' => $invoice['customer_name'] ?: $invoice['customer_email']
            ];
            header("Location: customer-portal.php");
            exit;
        } else {
            $error_message = "Invalid email or invoice number. Please check your details and try again.";
        }
    } else {
        $error_message = "Please enter both email and invoice number.";
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    unset($_SESSION['customer_portal']);
    header("Location: customer-portal.php");
    exit;
}

// Check if customer is logged in
$is_logged_in = isset($_SESSION['customer_portal']);
$customer_invoices = [];

if ($is_logged_in) {
    $customer_email = $_SESSION['customer_portal']['email'];
    
    // Get all invoices for this customer
    $stmt = $conn->prepare("
        SELECT i.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone, c.address as customer_address
        FROM invoices i 
        LEFT JOIN customers c ON i.customer_id = c.id 
        WHERE i.customer_email = ? OR c.email = ?
        ORDER BY i.created_at DESC
    ");
    $stmt->bind_param("ss", $customer_email, $customer_email);
    $stmt->execute();
    $result = $stmt->get_result();
    $customer_invoices = $result->fetch_all(MYSQLI_ASSOC);
}

$company_info = getCompanyInfo();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - <?php echo htmlspecialchars($company_info['company_name']); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'som-blue': '#2563EB',
                        'som-light-blue': '#3B82F6',
                        'som-green': '#059669',
                        'som-light-green': '#10B981',
                        'som-gray': '#F8FAFC',
                        'som-dark': '#1E293B',
                        'som-red': '#DC2626',
                        'som-border': '#E2E8F0'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-som-gray min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-som-border">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <?php if (!empty($company_info['logo_path']) && file_exists($company_info['logo_path'])): ?>
                        <img src="<?php echo htmlspecialchars($company_info['logo_path']); ?>" 
                             alt="Company Logo" class="h-10 w-auto mr-3">
                    <?php endif; ?>
                    <div>
                        <h1 class="text-xl font-bold text-som-dark"><?php echo htmlspecialchars($company_info['company_name']); ?></h1>
                        <p class="text-sm text-gray-600">Customer Portal</p>
                    </div>
                </div>
                
                <?php if ($is_logged_in): ?>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">
                            Welcome, <?php echo htmlspecialchars($_SESSION['customer_portal']['customer_name']); ?>
                        </span>
                        <a href="?logout=1" class="text-som-blue hover:text-som-light-blue text-sm font-medium">
                            <i class="fas fa-sign-out-alt mr-1"></i>Logout
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <div class="container mx-auto px-4 py-8">
        <?php if (!$is_logged_in): ?>
            <!-- Login Form -->
            <div class="max-w-md mx-auto">
                <div class="bg-white rounded-lg shadow-md p-8">
                    <div class="text-center mb-6">
                        <i class="fas fa-user-circle text-4xl text-som-blue mb-3"></i>
                        <h2 class="text-2xl font-bold text-som-dark">Access Your Invoices</h2>
                        <p class="text-gray-600 mt-2">Enter your email and invoice number to view your invoices</p>
                    </div>

                    <?php if (isset($error_message)): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?php echo $error_message; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="customer_login" value="1">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" name="email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                   placeholder="Enter your email address">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Invoice Number</label>
                            <input type="text" name="invoice_number" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-som-blue focus:border-transparent"
                                   placeholder="Enter your invoice number">
                        </div>
                        
                        <button type="submit" class="w-full bg-som-blue hover:bg-som-light-blue text-white font-medium py-2 px-4 rounded-md transition-colors">
                            <i class="fas fa-sign-in-alt mr-2"></i>Access Portal
                        </button>
                    </form>

                    <div class="mt-6 pt-6 border-t border-gray-200 text-center">
                        <p class="text-sm text-gray-600">
                            Need help? Contact us at 
                            <a href="mailto:<?php echo htmlspecialchars($company_info['email']); ?>" 
                               class="text-som-blue hover:text-som-light-blue font-medium">
                                <?php echo htmlspecialchars($company_info['email']); ?>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Customer Dashboard -->
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-som-dark mb-2">Your Invoices</h2>
                <p class="text-gray-600">View and manage your invoices from <?php echo htmlspecialchars($company_info['company_name']); ?></p>
            </div>

            <?php if (empty($customer_invoices)): ?>
                <div class="bg-white rounded-lg shadow-md p-8 text-center">
                    <i class="fas fa-file-invoice text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-medium text-gray-900 mb-2">No Invoices Found</h3>
                    <p class="text-gray-600">We couldn't find any invoices associated with your email address.</p>
                </div>
            <?php else: ?>
                <!-- Invoice Statistics -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <?php
                    $total_invoices = count($customer_invoices);
                    $total_amount = array_sum(array_column($customer_invoices, 'total_amount'));
                    $paid_invoices = count(array_filter($customer_invoices, function($inv) { return $inv['status'] == 'paid'; }));
                    $pending_invoices = count(array_filter($customer_invoices, function($inv) { return $inv['status'] == 'pending'; }));
                    ?>
                    
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="p-3 bg-blue-100 rounded-full">
                                <i class="fas fa-file-invoice text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Invoices</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo $total_invoices; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="p-3 bg-green-100 rounded-full">
                                <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Total Amount</p>
                                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($total_amount, 2); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="p-3 bg-green-100 rounded-full">
                                <i class="fas fa-check-circle text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Paid</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo $paid_invoices; ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center">
                            <div class="p-3 bg-yellow-100 rounded-full">
                                <i class="fas fa-clock text-yellow-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Pending</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo $pending_invoices; ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoices Table -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Invoice History</h3>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($customer_invoices as $invoice): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo date('M j, Y', strtotime($invoice['invoice_date'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo $invoice['due_date'] ? date('M j, Y', strtotime($invoice['due_date'])) : 'N/A'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            $<?php echo number_format($invoice['total_amount'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $status_colors = [
                                                'draft' => 'bg-gray-100 text-gray-800',
                                                'pending' => 'bg-yellow-100 text-yellow-800',
                                                'paid' => 'bg-green-100 text-green-800',
                                                'overdue' => 'bg-red-100 text-red-800',
                                                'cancelled' => 'bg-red-100 text-red-800'
                                            ];
                                            $status_class = $status_colors[$invoice['status']] ?? 'bg-gray-100 text-gray-800';
                                            ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                                <?php echo ucfirst($invoice['status']); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="generate-pdf.php?id=<?php echo $invoice['id']; ?>" target="_blank"
                                                   class="text-som-blue hover:text-som-light-blue">
                                                    <i class="fas fa-download mr-1"></i>Download
                                                </a>
                                                <?php if ($invoice['status'] == 'pending' || $invoice['status'] == 'overdue'): ?>
                                                    <a href="customer-payment.php?invoice=<?php echo $invoice['id']; ?>"
                                                       class="text-som-green hover:text-som-light-green">
                                                        <i class="fas fa-credit-card mr-1"></i>Pay Now
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-som-border mt-12">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center text-sm text-gray-600">
                <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars($company_info['company_name']); ?>. All rights reserved.</p>
                <?php if ($company_info['phone'] || $company_info['email']): ?>
                    <p class="mt-2">
                        <?php if ($company_info['phone']): ?>
                            <span>Phone: <?php echo htmlspecialchars($company_info['phone']); ?></span>
                        <?php endif; ?>
                        <?php if ($company_info['phone'] && $company_info['email']): ?>
                            <span class="mx-2">|</span>
                        <?php endif; ?>
                        <?php if ($company_info['email']): ?>
                            <span>Email: <?php echo htmlspecialchars($company_info['email']); ?></span>
                        <?php endif; ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    </footer>
</body>
</html>
