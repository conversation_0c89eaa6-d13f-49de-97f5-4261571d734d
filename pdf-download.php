<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get invoice ID
$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$invoice_id) {
    die('Invalid invoice ID');
}

// Get invoice data
$invoice = getInvoiceById($invoice_id);
if (!$invoice) {
    die('Invoice not found');
}

// Get company information
$company_info = getCompanyInfo();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?> - PDF Download</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        
        .pdf-instructions {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .pdf-instructions h1 {
            color: #2563eb;
            margin-bottom: 20px;
        }
        
        .pdf-instructions .invoice-info {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .pdf-instructions .steps {
            text-align: left;
            margin: 30px 0;
        }
        
        .pdf-instructions .steps ol {
            padding-left: 20px;
        }
        
        .pdf-instructions .steps li {
            margin: 10px 0;
            font-size: 16px;
        }
        
        .pdf-buttons {
            margin: 30px 0;
        }
        
        .pdf-buttons button {
            background: #2563eb;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            transition: background-color 0.3s;
        }
        
        .pdf-buttons button:hover {
            background: #1d4ed8;
        }
        
        .pdf-buttons .secondary {
            background: #6b7280;
        }
        
        .pdf-buttons .secondary:hover {
            background: #4b5563;
        }
        
        .pdf-buttons .success {
            background: #059669;
        }
        
        .pdf-buttons .success:hover {
            background: #047857;
        }
        
        .browser-instructions {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .browser-instructions h3 {
            color: #92400e;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <div class="pdf-instructions">
        <h1><i class="fas fa-file-pdf"></i> Download Invoice as PDF</h1>
        
        <div class="invoice-info">
            <h3>Invoice Details</h3>
            <p><strong>Invoice Number:</strong> <?php echo htmlspecialchars($invoice['invoice_number']); ?></p>
            <p><strong>Customer:</strong> <?php echo htmlspecialchars($invoice['customer_name']); ?></p>
            <p><strong>Amount:</strong> $<?php echo number_format($invoice['total_amount'], 2); ?></p>
            <p><strong>Date:</strong> <?php echo date('M j, Y', strtotime($invoice['invoice_date'])); ?></p>
        </div>
        
        <div class="browser-instructions">
            <h3><i class="fas fa-info-circle"></i> How to Save as PDF</h3>
            <div class="steps">
                <ol>
                    <li>Click the <strong>"Open Print-Ready Invoice"</strong> button below</li>
                    <li>In the new window, press <strong>Ctrl+P</strong> (Windows) or <strong>Cmd+P</strong> (Mac)</li>
                    <li>In the print dialog, select <strong>"Save as PDF"</strong> or <strong>"Microsoft Print to PDF"</strong></li>
                    <li>Choose your save location and filename</li>
                    <li>Click <strong>"Save"</strong></li>
                </ol>
            </div>
        </div>
        
        <div class="pdf-buttons">
            <button onclick="openPrintVersion()" class="success">
                <i class="fas fa-external-link-alt"></i> Open Print-Ready Invoice
            </button>
            <button onclick="goBack()" class="secondary">
                <i class="fas fa-arrow-left"></i> Go Back
            </button>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280;">
            <p><small><i class="fas fa-lightbulb"></i> <strong>Tip:</strong> The print-ready version is optimized for PDF conversion with proper formatting and no background colors.</small></p>
        </div>
    </div>

    <script>
        function openPrintVersion() {
            // Open the print-ready version in a new window
            const printWindow = window.open('generate-pdf.php?id=<?php echo $invoice_id; ?>&print=1', '_blank', 'width=800,height=600');
            
            // Focus the new window
            if (printWindow) {
                printWindow.focus();
            }
        }
        
        function goBack() {
            // Go back to the previous page or invoice view
            if (document.referrer) {
                window.location.href = document.referrer;
            } else {
                window.location.href = 'view-invoice.php?id=<?php echo $invoice_id; ?>';
            }
        }
    </script>
</body>
</html>
