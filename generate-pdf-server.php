<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get invoice ID
$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$invoice_id) {
    die('Invalid invoice ID');
}

// Get invoice data
$invoice = getInvoiceById($invoice_id);
if (!$invoice) {
    die('Invoice not found');
}

// Get company information
$company_info = getCompanyInfo();

// Check available PDF generation methods
$pdf_methods = [];

// Check wkhtmltopdf
$output = [];
$return_var = 0;
exec('wkhtmltopdf --version 2>&1', $output, $return_var);
if ($return_var === 0) {
    $pdf_methods['wkhtmltopdf'] = true;
} else {
    $pdf_methods['wkhtmltopdf'] = false;
}

// Check if we can use browser automation (headless Chrome/Chromium)
$output = [];
$return_var = 0;
exec('google-chrome --version 2>&1', $output, $return_var);
if ($return_var === 0) {
    $pdf_methods['chrome'] = true;
} else {
    $pdf_methods['chrome'] = false;
}

// Check if Puppeteer or similar is available
$pdf_methods['puppeteer'] = file_exists('node_modules/puppeteer/index.js');

$wkhtmltopdf_available = $pdf_methods['wkhtmltopdf'];

// If wkhtmltopdf is available, generate PDF server-side
if ($wkhtmltopdf_available && isset($_GET['action']) && $_GET['action'] === 'download') {
    // Create temporary HTML file
    $temp_html = tempnam(sys_get_temp_dir(), 'invoice_') . '.html';
    $temp_pdf = tempnam(sys_get_temp_dir(), 'invoice_') . '.pdf';
    
    // Generate HTML content
    ob_start();
    include 'generate-pdf.php';
    $html_content = ob_get_clean();
    
    // Write HTML to temporary file
    file_put_contents($temp_html, $html_content);
    
    // Generate PDF using wkhtmltopdf
    $command = "wkhtmltopdf --page-size A4 --margin-top 0.75in --margin-right 0.75in --margin-bottom 0.75in --margin-left 0.75in --encoding UTF-8 --quiet " . escapeshellarg($temp_html) . " " . escapeshellarg($temp_pdf);
    
    exec($command, $output, $return_var);
    
    if ($return_var === 0 && file_exists($temp_pdf)) {
        // Send PDF to browser
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="invoice-' . $invoice['invoice_number'] . '.pdf"');
        header('Content-Length: ' . filesize($temp_pdf));
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        
        readfile($temp_pdf);
        
        // Clean up temporary files
        unlink($temp_html);
        unlink($temp_pdf);
        exit;
    } else {
        // Clean up temporary files
        if (file_exists($temp_html)) unlink($temp_html);
        if (file_exists($temp_pdf)) unlink($temp_pdf);
        
        // Fall back to browser-based PDF generation
        header("Location: pdf-download.php?id=" . $invoice_id);
        exit;
    }
}

// If server-side PDF generation is not available, show information page
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>PDF Generation - Som Milk Invoice System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8fafc;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .status-info {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .status-warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        
        .status-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .buttons {
            margin: 30px 0;
            text-align: center;
        }
        
        .buttons button, .buttons a {
            background: #2563eb;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s;
        }
        
        .buttons button:hover, .buttons a:hover {
            background: #1d4ed8;
        }
        
        .buttons .secondary {
            background: #6b7280;
        }
        
        .buttons .secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-file-pdf"></i> PDF Generation Status</h1>
        
        <?php if ($wkhtmltopdf_available): ?>
            <div class="status-info status-success">
                <h3><i class="fas fa-check-circle"></i> Server-side PDF Generation Available</h3>
                <p>Your server supports automatic PDF generation. You can download PDFs directly.</p>
            </div>
            
            <div class="buttons">
                <a href="generate-pdf-server.php?id=<?php echo $invoice_id; ?>&action=download">
                    <i class="fas fa-download"></i> Download PDF
                </a>
                <a href="generate-pdf.php?id=<?php echo $invoice_id; ?>" target="_blank">
                    <i class="fas fa-eye"></i> View Invoice
                </a>
                <a href="view-invoice.php?id=<?php echo $invoice_id; ?>" class="secondary">
                    <i class="fas fa-arrow-left"></i> Back to Invoice
                </a>
            </div>
        <?php else: ?>
            <div class="status-info status-warning">
                <h3><i class="fas fa-exclamation-triangle"></i> Browser-based PDF Generation</h3>
                <p>Server-side PDF generation is not available. You can still create PDFs using your browser's print function.</p>
            </div>
            
            <div class="buttons">
                <a href="pdf-download.php?id=<?php echo $invoice_id; ?>">
                    <i class="fas fa-download"></i> Get PDF Instructions
                </a>
                <a href="generate-pdf.php?id=<?php echo $invoice_id; ?>" target="_blank">
                    <i class="fas fa-eye"></i> View Print-Ready Invoice
                </a>
                <a href="view-invoice.php?id=<?php echo $invoice_id; ?>" class="secondary">
                    <i class="fas fa-arrow-left"></i> Back to Invoice
                </a>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280;">
            <h4>System Information:</h4>
            <ul>
                <li><strong>wkhtmltopdf:</strong> <?php echo $pdf_methods['wkhtmltopdf'] ? 'Available' : 'Not Available'; ?></li>
                <li><strong>Chrome/Chromium:</strong> <?php echo $pdf_methods['chrome'] ? 'Available' : 'Not Available'; ?></li>
                <li><strong>Puppeteer:</strong> <?php echo $pdf_methods['puppeteer'] ? 'Available' : 'Not Available'; ?></li>
                <li><strong>PHP Version:</strong> <?php echo phpversion(); ?></li>
                <li><strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
                <li><strong>Operating System:</strong> <?php echo php_uname('s') . ' ' . php_uname('r'); ?></li>
            </ul>

            <div style="margin-top: 15px; padding: 10px; background: #f8fafc; border-radius: 5px; font-size: 12px;">
                <strong>Recommendation:</strong> For the best PDF generation experience, install wkhtmltopdf on your server.
                Until then, the browser-based PDF generation works well for most use cases.
            </div>
        </div>
    </div>
</body>
</html>
