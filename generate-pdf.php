<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Get invoice ID
$invoice_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$invoice_id) {
    die('Invalid invoice ID');
}

// Get invoice data
$invoice = getInvoiceById($invoice_id);
if (!$invoice) {
    die('Invoice not found');
}

// Get company information
$company_info = getCompanyInfo();

// For now, we'll create a simple HTML-to-PDF solution
// In production, you might want to use libraries like TCPDF, FPDF, or mPDF

// Generate HTML content for PDF
ob_start();
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice <?php echo htmlspecialchars($invoice['invoice_number']); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 20px;
        }
        .company-info {
            flex: 1;
            display: flex;
            align-items: flex-start;
        }
        .company-logo {
            margin-right: 20px;
            flex-shrink: 0;
        }
        .company-logo img {
            max-width: 80px;
            max-height: 80px;
            object-fit: contain;
        }
        .company-details-wrapper {
            flex: 1;
        }
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }
        .company-details {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        .invoice-info {
            text-align: right;
            flex: 1;
        }
        .invoice-title {
            font-size: 32px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }
        .invoice-number {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .invoice-dates {
            font-size: 14px;
            color: #666;
        }
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .billing-info {
            flex: 1;
            margin-right: 20px;
        }
        .billing-title {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
        }
        .billing-details {
            font-size: 14px;
            line-height: 1.6;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .items-table th {
            background-color: #1e40af;
            color: white;
            padding: 12px;
            text-align: left;
            font-weight: bold;
        }
        .items-table td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .items-table tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals-section {
            float: right;
            width: 300px;
            margin-bottom: 30px;
        }
        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }
        .totals-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .totals-table .total-row {
            background-color: #1e40af;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        .notes-section {
            clear: both;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        .notes-title {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }
        .notes-content {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #1e40af;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .banking-info {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .banking-title {
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 10px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <!-- Print Button (hidden in PDF) -->
    <div class="no-print" style="text-align: right; margin-bottom: 20px;">
        <button onclick="window.print()" style="background: #1e40af; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            Print Invoice
        </button>
        <button onclick="window.close()" style="background: #6b7280; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>

    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="company-info">
            <?php if (!empty($company_info['logo_path']) && file_exists($company_info['logo_path'])): ?>
                <div class="company-logo">
                    <img src="<?php echo htmlspecialchars($company_info['logo_path']); ?>" alt="Company Logo">
                </div>
            <?php endif; ?>
            <div class="company-details-wrapper">
                <div class="company-name"><?php echo htmlspecialchars($company_info['company_name']); ?></div>
                <div class="company-details">
                    <?php if ($company_info['address']): ?>
                        <?php echo htmlspecialchars($company_info['address']); ?><br>
                    <?php endif; ?>
                    <?php if ($company_info['city'] || $company_info['state'] || $company_info['zip_code']): ?>
                        <?php echo htmlspecialchars($company_info['city']); ?>
                        <?php echo $company_info['state'] ? ', ' . htmlspecialchars($company_info['state']) : ''; ?>
                        <?php echo htmlspecialchars($company_info['zip_code']); ?><br>
                    <?php endif; ?>
                    <?php if ($company_info['phone']): ?>
                        Phone: <?php echo htmlspecialchars($company_info['phone']); ?><br>
                    <?php endif; ?>
                    <?php if ($company_info['email']): ?>
                        Email: <?php echo htmlspecialchars($company_info['email']); ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="invoice-info">
            <div class="invoice-title">INVOICE</div>
            <div class="invoice-number"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
            <div class="invoice-dates">
                <strong>Date:</strong> <?php echo date('F j, Y', strtotime($invoice['invoice_date'])); ?><br>
                <?php if ($invoice['due_date']): ?>
                    <strong>Due Date:</strong> <?php echo date('F j, Y', strtotime($invoice['due_date'])); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Billing Information -->
    <div class="billing-section">
        <div class="billing-info">
            <div class="billing-title">Bill To:</div>
            <div class="billing-details">
                <strong><?php echo htmlspecialchars($invoice['customer_name']); ?></strong><br>
                <?php if ($invoice['customer_address']): ?>
                    <?php echo nl2br(htmlspecialchars($invoice['customer_address'])); ?><br>
                <?php endif; ?>
                <?php if ($invoice['customer_email']): ?>
                    Email: <?php echo htmlspecialchars($invoice['customer_email']); ?><br>
                <?php endif; ?>
                <?php if ($invoice['customer_phone']): ?>
                    Phone: <?php echo htmlspecialchars($invoice['customer_phone']); ?>
                <?php endif; ?>
            </div>
        </div>
        <div class="billing-info">
            <div class="billing-title">Invoice Status:</div>
            <div class="billing-details">
                <strong style="color: <?php echo $invoice['status'] == 'paid' ? '#10b981' : ($invoice['status'] == 'overdue' ? '#ef4444' : '#f59e0b'); ?>">
                    <?php echo strtoupper($invoice['status']); ?>
                </strong>
            </div>
        </div>
    </div>

    <!-- Invoice Items -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 15%;" class="text-center">Quantity</th>
                <th style="width: 15%;" class="text-right">Unit Price</th>
                <th style="width: 20%;" class="text-right">Total</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($invoice['items'] as $item): ?>
                <tr>
                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                    <td class="text-center"><?php echo number_format($item['quantity'], 2); ?></td>
                    <td class="text-right">$<?php echo number_format($item['unit_price'], 2); ?></td>
                    <td class="text-right">$<?php echo number_format($item['total_price'], 2); ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Totals -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td><strong>Subtotal:</strong></td>
                <td class="text-right">$<?php echo number_format($invoice['subtotal'], 2); ?></td>
            </tr>
            <?php if ($invoice['tax_amount'] > 0): ?>
                <tr>
                    <td><strong>Tax:</strong></td>
                    <td class="text-right">$<?php echo number_format($invoice['tax_amount'], 2); ?></td>
                </tr>
            <?php endif; ?>
            <tr class="total-row">
                <td><strong>TOTAL:</strong></td>
                <td class="text-right"><strong>$<?php echo number_format($invoice['total_amount'], 2); ?></strong></td>
            </tr>
        </table>
    </div>

    <!-- Notes -->
    <?php if ($invoice['notes']): ?>
        <div class="notes-section">
            <div class="notes-title">Notes:</div>
            <div class="notes-content">
                <?php echo nl2br(htmlspecialchars($invoice['notes'])); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Banking Information -->
    <?php if ($company_info['bank_name'] || $company_info['account_number']): ?>
        <div class="banking-info">
            <div class="banking-title">Payment Information:</div>
            <?php if ($company_info['bank_name']): ?>
                <strong>Bank:</strong> <?php echo htmlspecialchars($company_info['bank_name']); ?><br>
            <?php endif; ?>
            <?php if ($company_info['account_number']): ?>
                <strong>Account Number:</strong> <?php echo htmlspecialchars($company_info['account_number']); ?><br>
            <?php endif; ?>
            <?php if ($company_info['routing_number']): ?>
                <strong>Routing Number:</strong> <?php echo htmlspecialchars($company_info['routing_number']); ?><br>
            <?php endif; ?>
            <?php if ($company_info['account_holder_name']): ?>
                <strong>Account Holder:</strong> <?php echo htmlspecialchars($company_info['account_holder_name']); ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Footer -->
    <div class="footer">
        <p>Thank you for your business!</p>
        <p>This invoice was generated on <?php echo date('F j, Y \a\t g:i A'); ?></p>
    </div>

    <script>
        // Auto-print when opened in new window
        if (window.location.search.includes('print=1')) {
            window.onload = function() {
                window.print();
            };
        }
    </script>
</body>
</html>
<?php
$html_content = ob_get_clean();

// Check if we want to display HTML or generate PDF
$action = isset($_GET['action']) ? $_GET['action'] : 'view';

if ($action === 'download') {
    // For PDF download, we'll use a simple approach
    // In production, consider using libraries like mPDF, TCPDF, or wkhtmltopdf
    
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="invoice-' . $invoice['invoice_number'] . '.pdf"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    // Simple PDF generation (this is a basic implementation)
    // For better PDF generation, integrate a proper PDF library
    echo $html_content;
} else {
    // Display HTML version
    echo $html_content;
}
?>
