<?php
require_once 'includes/config.php';

// Check if file parameter is provided
if (!isset($_GET['file']) || empty($_GET['file'])) {
    $_SESSION['error_message'] = "No backup file specified.";
    header("Location: backup-export.php");
    exit;
}

$filename = basename($_GET['file']); // Sanitize filename
$filepath = 'backups/' . $filename;

// Check if file exists and is a SQL file
if (!file_exists($filepath) || pathinfo($filename, PATHINFO_EXTENSION) !== 'sql') {
    $_SESSION['error_message'] = "Backup file not found or invalid.";
    header("Location: backup-export.php");
    exit;
}

// Delete the file
if (unlink($filepath)) {
    $_SESSION['success_message'] = "Backup file deleted successfully.";
} else {
    $_SESSION['error_message'] = "Failed to delete backup file.";
}

header("Location: backup-export.php");
exit;
?>
