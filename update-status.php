<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: invoices.php');
    exit;
}

// Get form data
$invoiceId = intval($_POST['invoice_id'] ?? 0);
$newStatus = $_POST['status'] ?? '';
$notes = $_POST['notes'] ?? '';

// Validate input
if ($invoiceId <= 0 || empty($newStatus)) {
    header('Location: invoices.php?error=' . urlencode('Invalid invoice ID or status.'));
    exit;
}

// Update status
$result = updateInvoiceStatus($invoiceId, $newStatus, $notes);

// Redirect back with message
$redirect = $_POST['redirect'] ?? 'invoices.php';
if ($result['success']) {
    header('Location: ' . $redirect . '?success=' . urlencode($result['message']));
} else {
    header('Location: ' . $redirect . '?error=' . urlencode($result['message']));
}
exit;
?>
