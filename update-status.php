<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: invoices.php');
    exit;
}

// Get form data
$invoiceId = intval($_POST['invoice_id'] ?? 0);
$newStatus = $_POST['status'] ?? '';
$notes = $_POST['notes'] ?? '';

// Validate input
if ($invoiceId <= 0 || empty($newStatus)) {
    $_SESSION['error_message'] = 'Invalid invoice ID or status.';
    header('Location: invoices.php');
    exit;
}

// Update status
$result = updateInvoiceStatus($invoiceId, $newStatus, $notes);

if ($result['success']) {
    $_SESSION['success_message'] = $result['message'];
} else {
    $_SESSION['error_message'] = $result['message'];
}

// Redirect back
$redirect = $_POST['redirect'] ?? 'invoices.php';
header('Location: ' . $redirect);
exit;
?>
