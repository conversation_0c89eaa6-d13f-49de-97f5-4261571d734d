#!/bin/bash

# Som Milk Invoice Management System
# Replit Setup Script

echo "Setting up Som Milk Invoice Management System for Replit..."

# Create necessary directories
mkdir -p uploads/invoices
chmod 755 uploads
chmod 755 uploads/invoices

# Initialize PostgreSQL if not already done
if [ ! -d "$HOME/.postgresql" ]; then
    echo "Initializing PostgreSQL..."
    initdb -D ~/.postgresql/data
    pg_ctl -D ~/.postgresql/data -l ~/.postgresql/logfile start
    
    # Create database
    createdb sommilk_invoice
    
    echo "PostgreSQL initialized and database created."
else
    echo "PostgreSQL already initialized."
    pg_ctl -D ~/.postgresql/data -l ~/.postgresql/logfile start
fi

# Set environment variables for database connection
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_NAME="sommilk_invoice"
export DB_USER="$USER"
export DB_PASSWORD=""

echo "Setup complete!"
echo "Visit the installation page to complete the setup: /install_postgresql.php"
echo "Or if already installed, go to: /index.php"
